! File: enkf_algorithms.f90
! Purpose: Numerical algorithms for Ensemble Kalman Filter
! Author: Auto-generated
! Date: 2025-08-31
! Version: 0.1

module enkf_algorithms
  implicit none
  private
  
  ! Public interface
  public :: EnKF_CalculateKalmanGain, EnKF_ApplyInflation, EnKF_ApplyLocalization
  public :: EnKF_GenerateObsPerturbations, EnKF_ApplyConstraints
  
  ! Parameters
  integer, parameter :: dp = kind(1.0d0)
  real(dp), parameter :: SMALL_NUMBER = 1.0d-12

contains

  !============================================================================
  subroutine EnKF_CalculateKalmanGain(X_pert, Y_pert, R_obs, K_gain, ierr)
    ! Calculate Kalman gain matrix K = PH^T (HPH + R)^-1
    ! Using ensemble covariances: P ≈ (1/(Ne-1)) X_pert X_pert^T
    
    real(dp), intent(in) :: X_pert(:,:)    ! (nstate, ensemble_size)
    real(dp), intent(in) :: Y_pert(:,:)    ! (nobs, ensemble_size)  
    real(dp), intent(in) :: R_obs(:,:)     ! (nobs, nobs)
    real(dp), intent(out) :: K_gain(:,:)   ! (nstate, nobs)
    integer, intent(out) :: ierr
    
    ! Local variables
    integer :: nstate, nobs, ne, i, j, info
    real(dp) :: scale_factor
    real(dp), allocatable :: HPH_R(:,:), PH(:,:), work(:)
    integer, allocatable :: ipiv(:)
    
    nstate = size(X_pert, 1)
    nobs = size(Y_pert, 1)
    ne = size(X_pert, 2)
    ierr = 0
    
    scale_factor = 1.0d0 / real(ne - 1, dp)
    
    ! Allocate working arrays
    allocate(HPH_R(nobs, nobs))
    allocate(PH(nstate, nobs))
    allocate(work(nobs))
    allocate(ipiv(nobs))
    
    ! Calculate PH^T = (1/(Ne-1)) X_pert Y_pert^T
    call dgemm('N', 'T', nstate, nobs, ne, scale_factor, &
               X_pert, nstate, Y_pert, nobs, 0.0d0, PH, nstate)
    
    ! Calculate HPH = (1/(Ne-1)) Y_pert Y_pert^T
    call dgemm('N', 'T', nobs, nobs, ne, scale_factor, &
               Y_pert, nobs, Y_pert, nobs, 0.0d0, HPH_R, nobs)
    
    ! Add observation error covariance: HPH + R
    HPH_R = HPH_R + R_obs
    
    ! Add diagonal regularization for numerical stability
    do i = 1, nobs
      HPH_R(i, i) = HPH_R(i, i) + SMALL_NUMBER
    enddo
    
    ! Solve for Kalman gain: K = PH^T * (HPH + R)^-1
    ! First, factorize HPH_R using LU decomposition
    call dgetrf(nobs, nobs, HPH_R, nobs, ipiv, info)
    if (info /= 0) then
      ierr = 1
      deallocate(HPH_R, PH, work, ipiv)
      return
    endif
    
    ! Solve K^T = (HPH + R)^-1 * (PH^T)^T = (HPH + R)^-1 * PH
    K_gain = transpose(PH)  ! K_gain^T = PH^T
    call dgetrs('N', nobs, nstate, HPH_R, nobs, ipiv, K_gain, nobs, info)
    if (info /= 0) then
      ierr = 2
      deallocate(HPH_R, PH, work, ipiv)
      return
    endif
    
    ! Transpose back to get K
    K_gain = transpose(K_gain)
    
    deallocate(HPH_R, PH, work, ipiv)
    
  end subroutine EnKF_CalculateKalmanGain

  !============================================================================
  subroutine EnKF_ApplyInflation(X_ensemble, ensemble_mean, inflation_factor)
    ! Apply multiplicative inflation to ensemble
    
    real(dp), intent(inout) :: X_ensemble(:,:)
    real(dp), intent(in) :: ensemble_mean(:)
    real(dp), intent(in) :: inflation_factor
    
    integer :: i, nstate, ne
    
    nstate = size(X_ensemble, 1)
    ne = size(X_ensemble, 2)
    
    if (inflation_factor <= 1.0d0) return
    
    ! Apply multiplicative inflation: x_i = x_mean + λ(x_i - x_mean)
    do i = 1, ne
      X_ensemble(:, i) = ensemble_mean + inflation_factor * (X_ensemble(:, i) - ensemble_mean)
    enddo
    
  end subroutine EnKF_ApplyInflation

  !============================================================================
  subroutine EnKF_ApplyLocalization(covariance, distances, localization_radius)
    ! Apply Gaspari-Cohn localization to covariance matrix
    
    real(dp), intent(inout) :: covariance(:,:)
    real(dp), intent(in) :: distances(:,:)
    real(dp), intent(in) :: localization_radius
    
    integer :: i, j, n
    real(dp) :: r, rho
    
    n = size(covariance, 1)
    
    do i = 1, n
      do j = 1, n
        r = distances(i, j) / localization_radius
        rho = gaspari_cohn_correlation(r)
        covariance(i, j) = covariance(i, j) * rho
      enddo
    enddo
    
  end subroutine EnKF_ApplyLocalization

  !============================================================================
  function gaspari_cohn_correlation(r) result(rho)
    ! Gaspari-Cohn localization function
    real(dp), intent(in) :: r
    real(dp) :: rho
    
    real(dp) :: r_abs
    
    r_abs = abs(r)
    
    if (r_abs >= 2.0d0) then
      rho = 0.0d0
    elseif (r_abs >= 1.0d0) then
      rho = (1.0d0/12.0d0) * r_abs**5 - 0.5d0 * r_abs**4 + (5.0d0/8.0d0) * r_abs**3 + &
            (5.0d0/3.0d0) * r_abs**2 - 5.0d0 * r_abs + 4.0d0 - (2.0d0/3.0d0) / r_abs
    else
      rho = -0.25d0 * r_abs**5 + 0.5d0 * r_abs**4 + (5.0d0/8.0d0) * r_abs**3 - &
            (5.0d0/3.0d0) * r_abs**2 + 1.0d0
    endif
    
  end function gaspari_cohn_correlation

  !============================================================================
  subroutine EnKF_GenerateObsPerturbations(obs_pert, R_obs, nobs, ne)
    ! Generate observation perturbations for stochastic EnKF
    
    integer, intent(in) :: nobs, ne
    real(dp), intent(out) :: obs_pert(nobs, ne)
    real(dp), intent(in) :: R_obs(nobs, nobs)
    
    integer :: i, j, info
    real(dp), allocatable :: L(:,:), work(:), random_vec(:)
    
    ! Allocate working arrays
    allocate(L(nobs, nobs))
    allocate(work(3*nobs))
    allocate(random_vec(nobs))
    
    ! Cholesky decomposition of R: R = L L^T
    L = R_obs
    call dpotrf('L', nobs, L, nobs, info)
    if (info /= 0) then
      ! Fallback: use diagonal approximation
      L = 0.0d0
      do i = 1, nobs
        L(i, i) = sqrt(max(R_obs(i, i), SMALL_NUMBER))
      enddo
    else
      ! Zero upper triangle
      do i = 1, nobs
        do j = i+1, nobs
          L(i, j) = 0.0d0
        enddo
      enddo
    endif
    
    ! Generate perturbations: e_i = L * z_i, where z_i ~ N(0,I)
    do i = 1, ne
      ! Generate random normal vector
      do j = 1, nobs
        call random_normal_single(random_vec(j))
      enddo
      
      ! Apply Cholesky factor: e = L * z
      call dgemv('N', nobs, nobs, 1.0d0, L, nobs, random_vec, 1, 0.0d0, obs_pert(:, i), 1)
    enddo
    
    deallocate(L, work, random_vec)
    
  end subroutine EnKF_GenerateObsPerturbations
  
  subroutine random_normal_single(x)
    ! Generate single random normal variable
    real(dp), intent(out) :: x
    real(dp) :: u1, u2
    
    call random_number(u1)
    call random_number(u2)
    x = sqrt(-2.0d0 * log(u1)) * cos(2.0d0 * 3.14159265359d0 * u2)
    
  end subroutine random_normal_single

end module enkf_algorithms
