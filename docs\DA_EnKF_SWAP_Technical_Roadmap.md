# SWAP 数据同化模块（EnKF）技术路线图

版本: v0.1（方案草案）
负责人: （待定）
目标: 在不改变 SWAP 核心数值解算逻辑的前提下，以“微创”方式为 SWAP（Fortran）增加每日频率的数据同化模块，采用集成卡尔曼滤波（Ensemble Kalman Filter, EnKF）对以下量进行同化：
- 土壤含水量 θ(z)
- <PERSON>（VG）土水特征参数（θr, θs, α, n）
- 叶面积指数（LAI）
- 作物模型参数（选定子集，如光合效率、分配系数等）

---

## 1. 现有代码库结构与日循环位置（依据 swap420）

关键文件与定位：
- `swap420/swap_main.f90`：主程序入口，三阶段调用 SWAP（初始化/动态/收尾）。
- `swap420/swap.f90`：模型主体，包含三大任务 iTask=1/2/3；其中 iTask=2 为“动态”阶段，包含逐日驱动与日末处理。
  - 每日开始：`if (flDayStart) then ... end if`
  - 日末逻辑：约在 `if (flDayEnd) then` 块内（行 253 附近）。
  - 与外部耦合接口：`contains` 内 `handle_exchange(task, flError)`，用于 DLL/exchange 模式。
- 土壤水分：`swap420/soilwater.f90`（状态/通量与更新）；`swap420/WC_K_models_04_11.f90`（VG 型参数与函数，如 WCr/WCs/Alpha/Npar/Mpar 等、K/θ-h 关系）。
- 作物与 LAI：`swap420/cropgrowth.f90`（作物生理与 LAI 更新）。
- 常用变量模块：`use variables, only: ...` 在多处出现（例如 `swap.f90` 中），可直接访问 `theta(:)`, `numnod`, `lai`, `rd`, `ch` 等。

结论：
- “日末”处（`if (flDayEnd) then` 块）是实施“每日同化”的首选钩子位置，不改变内部时间步长与收敛机制。
- 可在初始化（iTask=1）和收尾（iTask=3）分别放置 DA 初始化/清理调用。

---

## 2. 微创式数据同化模块总体设计

新增一个独立 Fortran 模块，不改动现有求解器与过程模块，尽量只在极少数位置插入调用：

新增文件建议：
- `swap420/da/swap_assimilation_enkf.f90`（新建目录 `da/`）：
  - `module swap_assimilation`
  - 对外 API：`DA_Init`, `DA_AssimilateDaily`, `DA_Finalize`
  - 内部：状态向量装配/回写、观测算子 H、EnKF 实现、噪声/通胀/本地化、I/O 与日志。
- `swap420/da/swap_assim_config.f90`：
  - 解析简单的 DA 配置文件（如 `da_config.ini`），包含同化频次、变量开关、误差协方差、集合规模、通胀/本地化参数、观测映射等。
- （可选）`swap420/da/obs_reader.f90`：
  - 观测文件逐日读取与对齐（支持 θ(z) 点/层、LAI、以及参数先验/约束）。

仅需在三处插入调用（微创）：
1) 初始化阶段（`swap.f90`, iTask==1）
   - 插入：`call DA_Init()`（读取 DA 配置，建立集合）。
2) 日末阶段（`swap.f90`, iTask==2, `if (flDayEnd) then` 内）
   - 插入：`call DA_AssimilateDaily()`（执行当天同化，更新所需状态/参数）。
   - 之后维持原有输出/管理流程。
3) 收尾阶段（`swap.f90`, iTask==3）
   - 插入：`call DA_Finalize()`（释放资源、汇总统计）。

说明：
- 以上 3 处为最小可用集成点，不改变原求解/时间推进逻辑；未来可通过编译开关或配置开关启闭。

---

## 3. 与现有 SWAP 的接口契约

模块 `swap_assimilation` 通过 `use variables, only: ...` 只读/读写必要量：
- 网格与状态：`numnod`, `dz(:)`, `theta(:)`, `h(:)`, `k(:)` 等。
- VG 参数（视模型设置为单/双孔隙）：`WCr(:)`, `WCs(:)`, `Alpha1(:)`, `Npar1(:)`, `Mpar1(:)`（以及双孔隙时的 `Alpha2/Npar2/Mpar2`）。
- 作物与 LAI：`lai`, `rd`, `ch` 及选定作物参数（如 `fl, fs, fo, fr, kdif, RGRLAI` 等，需列出白名单并做存在性检查）。

读写原则：
- θ(z)：直接在 `theta(1:numnod)` 上应用分析增益更新，随后调用现有过程以重算派生量（见 §6）。
- VG 参数：仅对配置允许的参数/层进行更新，更新后调用本地重计算（例如触发水力函数缓存刷新）。
- LAI：直接更新 `lai`，并确保不触发作物生命周期状态不一致（可设置上下界与平滑）。
- 作物参数：按白名单与维度一致性逐项更新；更新后可选择调用作物模块中的“派生参数”更新（若有）。

---

## 4. 观测数据整合

观测来源与格式（建议）：
- `obs/soil_moisture.csv`：列含日期、层中心深度或层号、θ 观测、观测误差；
- `obs/lai.csv`：日期、LAI、误差；
- `obs/params.csv`：日期、参数名、层/剖面、值、误差（用于软约束/弱同化）。

时间对齐与 QC：
- 采用“日末同化”：将所有当日观测聚合为观测向量 y_d；
- 对每类观测执行基本 QC（缺测/越界/极端值剔除），并构造观测误差协方差 R（可对角或分块稀疏）。

观测算子 H：
- 土壤含水量：选定层/节点的抽取算子（含从节点到层平均的映射矩阵）；
- LAI：H 为单位映射（从模型的 `lai`）；
- 参数观测/先验：H 为对相应参数单元的单位映射。

---

## 5. EnKF 实现细节

集合与扰动：
- 集合规模 Ne（例如 20–100，可配置）。
- 初始集合：围绕当前初值/先验，对 θ、VG 参数、LAI、作物参数按指定标准差/相关结构进行采样（高斯，支持截断与边界处理）。

预测步（forecast）：
- 日内采用 SWAP 原生时间步积分（已完成）；日末收敛后，当前（t_d）的集合状态即为先验 x_f。

分析步（analysis）：
- 组装先验均值 x̄_f 与扰动矩阵 X_f′；
- 生成观测扰动 E（若使用“随机观测 EnKF”）；
- 计算 Hx 与残差 d = (y + e) − H x_f；
- 协方差近似：Pf ≈ (1/(Ne−1)) X_f′ (X_f′)^T；
- 卡尔曼增益：K = Pf H^T (H Pf H^T + R)^−1；
- 逐成员更新：x_a = x_f + K d；
- 可选：协方差通胀（乘法/加法）与空间本地化（Gaspari–Cohn 核，按深度距离对 θ/VG 参数做局地化）。

约束与物理一致性：
- θ 约束在 [θr, θs]；
- VG 参数物理范围：θs∈(0,0.6]，θr∈[0,θs)，α>0，n>1，必要时将 m = 1−1/n；
- LAI ≥ 0，且可设上限；
- 作物参数各自边界（来自作物定义/文献）。

数值稳定性：
- 同化后对 θ 的变化幅度加“限制器”（例如逐层不超过某比例），减少跳变；
- 如果更新后的状态导致求解失败，可回退到保守缩放比率或跳过该日同化并记录。

---

## 6. 同化后的再一致化与重计算

为维持与 SWAP 内部派生量一致：
- θ 更新后：
  - 调用现有例程重算 K、C（导水率/微分含水容量）与平均导水率等（例如通过 `SoilWater(3)` 路径或专用状态刷新子程序，如有 `SoilWaterStateVar(2)` 等接口可用则优先）。
- VG 参数更新后：
  - 立即刷新与 VG 相关的函数表/缓存（位于 `WC_K_models_04_11.f90` 的函数在调用时读取参数，若存在缓存需触发刷新）。
- LAI/作物参数更新后：
  - 仅影响下一日的过程计算；无需回溯日内积分。

---

## 7. 错误处理与日志

- 统一使用 SWAP 现有 `fatalerr`/`logf` 机制：
  - 配置/观测文件缺失或不可解析 → 记录并将 `flDataAssim` 置为 false（软失败）；
  - 数值异常（协方差奇异、矩阵求逆失败）→ 使用 Tikhonov 正则或对角膨胀重试；
  - 单日同化失败 → 回退到先验，记录并继续模拟。
- DA 自有日志：`da/da.log`，包含每日日志、创新统计（O−H x）与 RMSE 轨迹。

---

## 8. 配置与文件组织

建议新增：
- `da/da_config.ini`：
  - 开关：`enable=true|false`，`frequency=daily`；
  - 集合规模：`ensemble_size=40`；
  - 误差：各变量的观测误差与初始分布；
  - 通胀与本地化参数；
  - 观测文件相对路径。
- `obs/*.csv`：观测数据。

文件/目录结构（新增部分）：
```
swap420/
  da/
    swap_assimilation_enkf.f90
    swap_assim_config.f90
    obs_reader.f90 (可选)
    da_config.ini (示例)
obs/
  soil_moisture.csv
  lai.csv
  params.csv
```

---

## 9. 具体实现步骤（里程碑）

M1 方案定稿与接口冻结（当前文档）
- 确认同化量、观测格式与白名单参数。
- 固化 3 个微创调用点与编译/运行开关。

M2 模块骨架与配置/观测读取
- 新建 `swap_assimilation_enkf.f90`，实现 `DA_Init/DA_AssimilateDaily/DA_Finalize` 空函数体与日志；
- 解析 `da_config.ini`，读取开关、集合规模、误差等；
- 观测读取与每日聚合（θ/LAI/参数）。

M3 状态向量装配/回写与观测算子
- θ/LAI/参数的装配、物理边界、白名单与映射（节点→层平均可选）；
- H 的实现与快速抽取。

M4 EnKF 核心
- 集合扰动生成、分析步（含正则化、通胀、本地化）；
- 数值稳健性处理与失败回退。

M5 微创集成与验证
- 在 `swap.f90` 三处插入调用（受 `flDataAssim` 或配置控制）；
- 小样例（无观测/假观测）跑通，验证对原结果零侵入（关闭 DA 时答案一致）。

M6 测试与基准
- 单元测试：观测读取/H/边界投影；
- 集成测试：日序列同化、RMSE 降低；
- 性能评估：集合规模对耗时影响。

---

## 10. 测试与验证策略

- 可复现实验：
  - Case A：无同化（baseline）
  - Case B：仅 LAI 同化
  - Case C：θ+LAI 同化
  - Case D：θ+LAI+VG 参数同化
- 评价指标：
  - 创新统计的零均值性、方差匹配；
  - 模拟与独立验证观测的 RMSE/BIAS；
  - 物理约束满足率与数值稳定性（失败/回退次数）。
- 自动化：
  - 编译开关或配置控制 DA；
  - 每日输出创新与分析增量。

---

## 11. 预期最小代码改动（示意）

仅在 `swap.f90` 插入 3 行（伪代码，实际按模块名与开关实现）：
- iTask == 1（初始化）附近：
  - `call DA_Init()`
- iTask == 2，`if (flDayEnd) then` 块内靠前位置（在输出与土壤营养之后、下一日控制之前）：
  - `call DA_AssimilateDaily()`
- iTask == 3（收尾）：
  - `call DA_Finalize()`

注：若需零改动主文件，可替代方案为通过 `handle_exchange`（DLL 模式）在任务 22/23/29 注入，但这要求外部驱动，不利于直接在原 SWAP 可执行内使用，故优先选择日末插桩的方案。

---

## 12. 风险与缓解

- 参数/状态维度高 → 协方差估计噪声大：使用本地化与适度通胀；
- VG 参数同化可能导致非物理曲线：严格边界与正则项；
- 观测稀疏 → 可采用层平均/垂向相关先验；
- 性能：优先 Fortran BLAS/LAPACK 求解小型线性系统（或自实现 Cholesky），避免大矩阵求逆。

---

## 13. 近期任务清单（可作为开发 Issue 列表）

1) 定稿与评审本方案（当前）
2) 创建 `swap420/da/` 目录与模块骨架文件
3) 定义 DA 配置与观测文件格式+样例
4) 实现 `DA_Init`（读配置+集合初始化）
5) 实现状态/参数装配与观测算子 H
6) 实现 EnKF 分析步、通胀与本地化（可先简化版）
7) 在 `swap.f90` 插入 3 个调用，受配置控制
8) 小样例运行与回归测试
9) 完善日志与错误处理，撰写用户文档

---

附：`swap_assimilation` API 草案（Fortran）

```fortran
module swap_assimilation
  implicit none
  private
  public :: DA_Init, DA_AssimilateDaily, DA_Finalize

contains
  subroutine DA_Init()
    ! 读取 da_config.ini，初始化集合与误差设置
  end subroutine

  subroutine DA_AssimilateDaily()
    ! 组装先验 x_f → 读入当日观测 y → 计算 K 并更新 θ/VG/LAI/参数
    ! 更新后触发必要的状态刷新（如导水率、微分含水容量）
  end subroutine

  subroutine DA_Finalize()
    ! 释放内存、输出统计
  end subroutine
end module
```



---

## 14. 方案完整性检查与可行性确认

- 覆盖性（需求匹配）
  - 每日频率：同化在日末 flDayEnd 钩子执行，满足“每日同化”要求。
  - 同化对象：θ(z)、VG 参数（θr, θs, α, n）、LAI、作物参数（白名单）均在状态向量中可选装配，并分别定义 H 与边界约束，满足需求。
- 微创性（最小改动）
  - 仅在 swap.f90 三处新增调用（Init/每日/Finalize），不改动数值求解与文件格式；通过配置开关 enable 控制，关闭时与原版字节一致的输出（回归测试验证）。
- 科学性与技术可行性（EnKF）
  - 采用增广状态（states+parameters）EnKF，参数演化用随机游走/收缩先验，已是主流做法；
  - 使用本地化减少虚假相关、通胀缓解协方差塌缩；
  - 对 VG 参数施加物理边界与 m=1−1/n 约束，避免非物理曲线；
  - 日末同化不破坏日内积分收敛性；更新后触发派生量刷新，数值路径闭合。

---

## 15. 任务清单细化（含输入/输出/验收/依赖）

说明：每个任务约 20 分钟；“验收”指可客观验证的完成标准；“依赖”引用前置任务编号。

M1 方案定稿与接口冻结
- M1-T1 审核需求与范围：输入=用户需求；输出=确认清单；验收=PO签字；依赖=无
- M1-T2 确认同化白名单参数：输入=作物参数表；输出=白名单v1；验收=文档合并；依赖=M1-T1
- M1-T3 决定 3 个插桩位置与开关：输入=swap.f90定位；输出=设计记录；验收=代码行号标注；依赖=M1-T1
- M1-T4 通过示意伪代码走查：输入=API草案；输出=评审结论；验收=通过/整改项清单；依赖=M1-T2,T3

M2 模块骨架与配置/观测读取
- M2-T1 创建目录与空模块：输入=repo；输出=da/文件骨架；验收=编译占位通过；依赖=M1
- M2-T2 实现配置解析（da_config.ini）：输入=示例ini；输出=Fortran读取子程序；验收=用例通过；依赖=M2-T1
- M2-T3 定义观测CSV格式与样例：输入=需求；输出=soil_moisture/lai/params样例；验收=CI读取样例OK；依赖=M2-T2
- M2-T4 实现按日期聚合观测：输入=CSV；输出=当日观测向量y与R；验收=单元测例；依赖=M2-T3
- M2-T5 日志器与错误处理框架：输入=logf接口；输出=da.log写入；验收=异常可捕获记录；依赖=M2-T1

M3 状态装配/回写与观测算子
- M3-T1 定义状态向量布局：输入=白名单与网格；输出=索引映射表；验收=打印比对；依赖=M2
- M3-T2 装配/拆解例程（X↔{theta,VG,LAI,crop}）：输入=变量模块；输出=子程序对；验收=往返一致性误差<1e-12；依赖=M3-T1
- M3-T3 物理边界约束器：输入=参数界；输出=clip/projection函数；验收=边界单测；依赖=M3-T2
- M3-T4 观测算子H构建（稀疏）：输入=观测定义；输出=结构体H；验收=H·x与直取一致；依赖=M2-T4,M3-T2
- M3-T5 缺测与层平均映射：输入=剖面定义；输出=权重矩阵；验收=权重和=1；依赖=M3-T4

M4 EnKF 核心
- M4-T1 集合初始化与扰动：输入=先验σ；输出=Ne成员；验收=样本统计匹配设定；依赖=M3
- M4-T2 先验扰动矩阵构建：输入=集合；输出=Xf′；验收=均值约0；依赖=M4-T1
- M4-T3 观测扰动生成：输入=R；输出=E；验收=样本协方差≈R；依赖=M2-T4
- M4-T4 K增益计算（Cholesky）：输入=H, Xf′, R；输出=K；验收=数值稳定无警告；依赖=M4-T2,M4-T3
- M4-T5 成员级更新与通胀：输入=K,d；输出=集合后验；验收=创新降低；依赖=M4-T4
- M4-T6 本地化（深度距离）：输入=距离矩阵；输出=ρ矩阵；验收=ρ(0)=1递减；依赖=M4-T4
- M4-T7 约束与限制器应用：输入=后验；输出=裁剪后状态；验收=无越界；依赖=M4-T5
- M4-T8 数值失败回退路径：输入=失败标志；输出=回退策略；验收=仿真不中断；依赖=M4-T5

M5 微创集成与验证
- M5-T1 在iTask=1插入DA_Init：输入=swap.f90；输出=1行调用；验收=编译通过；依赖=M4
- M5-T2 在日末插入DA_AssimilateDaily：输入=swap.f90；输出=1行调用；验收=小样例跑通；依赖=M5-T1
- M5-T3 在iTask=3插入DA_Finalize：输入=swap.f90；输出=1行调用；验收=日志完结；依赖=M5-T2
- M5-T4 配置开关回归：输入=enable=false；输出=结果比对一致；验收=数值一致性测试通过；依赖=M5-T2

M6 测试与基准
- M6-T1 单元测试（读取/H/装配/边界）：输入=样例；输出=pytest或自驱脚本；验收=全绿；依赖=M3
- M6-T2 集成测试（Case A–D）：输入=配置集；输出=RMSE报告；验收=创新降低；依赖=M5
- M6-T3 性能测试（Ne扫描）：输入=Ne列表；输出=耗时曲线；验收=曲线报告；依赖=M6-T2
- M6-T4 文档与用户指南：输入=实现；输出=README/用例；验收=第三方可复现；依赖=M6-T2

---

## 16. 技术细节补充

16.1 观测算子 H 的实现
- θ观测：
  - 节点观测：H 为选取矩阵（单位行向量放置到对应节点索引）。
  - 层/深度观测：预先构建权重 w_i（基于 dz 与层覆盖比例），H 行为 w 的稀疏表示，w·θ 近似层平均。
- LAI：H 为对 lai 分量的单位映射。
- 参数：H 对应参数分量的单位映射（可含层索引）。
- 接口草案：`call DA_BuildH(obs_defs, map, H_spr)`；`call DA_ApplyH(H_spr, x, yhat)`。

16.2 状态向量装配与回写
- 布局示例：x = [θ(1..numnod), VG(θr,θs,α,n)×层, LAI, 作物参数(白名单)]^T。
- 集合存储：`real(8) :: X(nstate, Ne)`；装配：`DA_PackState(X(:,m))`；回写：`DA_UnpackState(X(:,m))`。
- 约束：`DA_Constrain!(x)` 对 θ/参数裁剪，VG 保持 m=1−1/n 一致性；双孔隙配置仅在启用时装配对应参数。

16.3 错误处理与数值稳定
- 线性代数：优先 Cholesky（dpotrf/dpotrs），必要时加对角抖动 λI（λ≈1e-8×trace/ndim）。
- 条件数监控：若 cond(H Pf H^T + R) 过大，启用对角膨胀或增大R。
- 创新检验：|y−Hx̄_f|>kσ 触发鲁棒权重（Huber）或舍弃该观测。
- 限制器：单日 θ 变化比例上限（如 ≤0.1），避免大幅跳变。
- 回退：同化后若 SoilWater 后续刷新失败，则缩放更新因子 γ∈(0,1) 重试，失败则跳过当日。

16.4 与变量模块的接口规范
- 读取/写入：`use variables, only: numnod, dz, theta, h, k, lai, rd, ch`；VG 参数：`WCr, WCs, Alpha1, Npar1, Mpar1[, Alpha2, Npar2, Mpar2]`（若存在）。
- 精度：统一 real(8)；避免别名写入（对临时副本操作，再回写）。
- 特性开关：检测双孔隙/宏孔隙等标志，仅在启用时访问相应参数。

---

## 17. 实施可行性评估与时间线

- 主要风险与缓解
  - 协方差噪声与本地化选择：小样例网格上标定本地化半径；提供用户可调参数。
  - VG 参数非物理：边界+正则+小步长更新（限制器）。
  - 观测稀疏/不一致：严谨 QC 与单位/层匹配；必要时仅同化可观测子集。
  - 性能：Ne×nstate 增长带来的矩阵代价——采用稀疏H与小型逆（观测维度），并缓存映射。
- 粗略工作量（单人全时）
  - M1: 0.5 天；M2: 1.5 天；M3: 2 天；M4: 3 天；M5: 1.5 天；M6: 1.5 天；合计≈10 天。
- 里程碑时间线
  - 第1周：完成 M1–M3；第2周：完成 M4–M6，交付文档与测试报告。
