# SWAP 数据同化模块（EnKF）技术路线图

版本: v0.1（方案草案）
负责人: （待定）
目标: 在不改变 SWAP 核心数值解算逻辑的前提下，以“微创”方式为 SWAP（Fortran）增加每日频率的数据同化模块，采用集成卡尔曼滤波（Ensemble Kalman Filter, EnKF）对以下量进行同化：
- 土壤含水量 θ(z)
- <PERSON>（VG）土水特征参数（θr, θs, α, n）
- 叶面积指数（LAI）
- 作物模型参数（选定子集，如光合效率、分配系数等）

---

## 1. 现有代码库结构与日循环位置（依据 swap420）

关键文件与定位：
- `swap420/swap_main.f90`：主程序入口，三阶段调用 SWAP（初始化/动态/收尾）。
- `swap420/swap.f90`：模型主体，包含三大任务 iTask=1/2/3；其中 iTask=2 为“动态”阶段，包含逐日驱动与日末处理。
  - 每日开始：`if (flDayStart) then ... end if`
  - 日末逻辑：约在 `if (flDayEnd) then` 块内（行 253 附近）。
  - 与外部耦合接口：`contains` 内 `handle_exchange(task, flError)`，用于 DLL/exchange 模式。
- 土壤水分：`swap420/soilwater.f90`（状态/通量与更新）；`swap420/WC_K_models_04_11.f90`（VG 型参数与函数，如 WCr/WCs/Alpha/Npar/Mpar 等、K/θ-h 关系）。
- 作物与 LAI：`swap420/cropgrowth.f90`（作物生理与 LAI 更新）。
- 常用变量模块：`use variables, only: ...` 在多处出现（例如 `swap.f90` 中），可直接访问 `theta(:)`, `numnod`, `lai`, `rd`, `ch` 等。

结论：
- “日末”处（`if (flDayEnd) then` 块）是实施“每日同化”的首选钩子位置，不改变内部时间步长与收敛机制。
- 可在初始化（iTask=1）和收尾（iTask=3）分别放置 DA 初始化/清理调用。

---

## 2. 微创式数据同化模块总体设计

新增一个独立 Fortran 模块，不改动现有求解器与过程模块，尽量只在极少数位置插入调用：

新增文件建议：
- `swap420/da/swap_assimilation_enkf.f90`（新建目录 `da/`）：
  - `module swap_assimilation`
  - 对外 API：`DA_Init`, `DA_AssimilateDaily`, `DA_Finalize`
  - 内部：状态向量装配/回写、观测算子 H、EnKF 实现、噪声/通胀/本地化、I/O 与日志。
- `swap420/da/swap_assim_config.f90`：
  - 解析简单的 DA 配置文件（如 `da_config.ini`），包含同化频次、变量开关、误差协方差、集合规模、通胀/本地化参数、观测映射等。
- （可选）`swap420/da/obs_reader.f90`：
  - 观测文件逐日读取与对齐（支持 θ(z) 点/层、LAI、以及参数先验/约束）。

仅需在三处插入调用（微创）：
1) 初始化阶段（`swap.f90`, iTask==1）
   - 插入：`call DA_Init()`（读取 DA 配置，建立集合）。
2) 日末阶段（`swap.f90`, iTask==2, `if (flDayEnd) then` 内）
   - 插入：`call DA_AssimilateDaily()`（执行当天同化，更新所需状态/参数）。
   - 之后维持原有输出/管理流程。
3) 收尾阶段（`swap.f90`, iTask==3）
   - 插入：`call DA_Finalize()`（释放资源、汇总统计）。

说明：
- 以上 3 处为最小可用集成点，不改变原求解/时间推进逻辑；未来可通过编译开关或配置开关启闭。

---

## 3. 与现有 SWAP 的接口契约

模块 `swap_assimilation` 通过 `use variables, only: ...` 只读/读写必要量：
- 网格与状态：`numnod`, `dz(:)`, `theta(:)`, `h(:)`, `k(:)` 等。
- VG 参数（视模型设置为单/双孔隙）：`WCr(:)`, `WCs(:)`, `Alpha1(:)`, `Npar1(:)`, `Mpar1(:)`（以及双孔隙时的 `Alpha2/Npar2/Mpar2`）。
- 作物与 LAI：`lai`, `rd`, `ch` 及选定作物参数（如 `fl, fs, fo, fr, kdif, RGRLAI` 等，需列出白名单并做存在性检查）。

读写原则：
- θ(z)：直接在 `theta(1:numnod)` 上应用分析增益更新，随后调用现有过程以重算派生量（见 §6）。
- VG 参数：仅对配置允许的参数/层进行更新，更新后调用本地重计算（例如触发水力函数缓存刷新）。
- LAI：直接更新 `lai`，并确保不触发作物生命周期状态不一致（可设置上下界与平滑）。
- 作物参数：按白名单与维度一致性逐项更新；更新后可选择调用作物模块中的“派生参数”更新（若有）。

---

## 4. 观测数据整合

观测来源与格式（建议）：
- `obs/soil_moisture.csv`：列含日期、层中心深度或层号、θ 观测、观测误差；
- `obs/lai.csv`：日期、LAI、误差；
- `obs/params.csv`：日期、参数名、层/剖面、值、误差（用于软约束/弱同化）。

时间对齐与 QC：
- 采用“日末同化”：将所有当日观测聚合为观测向量 y_d；
- 对每类观测执行基本 QC（缺测/越界/极端值剔除），并构造观测误差协方差 R（可对角或分块稀疏）。

观测算子 H：
- 土壤含水量：选定层/节点的抽取算子（含从节点到层平均的映射矩阵）；
- LAI：H 为单位映射（从模型的 `lai`）；
- 参数观测/先验：H 为对相应参数单元的单位映射。

---

## 5. EnKF 实现细节

集合与扰动：
- 集合规模 Ne（例如 20–100，可配置）。
- 初始集合：围绕当前初值/先验，对 θ、VG 参数、LAI、作物参数按指定标准差/相关结构进行采样（高斯，支持截断与边界处理）。

预测步（forecast）：
- 日内采用 SWAP 原生时间步积分（已完成）；日末收敛后，当前（t_d）的集合状态即为先验 x_f。

分析步（analysis）：
- 组装先验均值 x̄_f 与扰动矩阵 X_f′；
- 生成观测扰动 E（若使用“随机观测 EnKF”）；
- 计算 Hx 与残差 d = (y + e) − H x_f；
- 协方差近似：Pf ≈ (1/(Ne−1)) X_f′ (X_f′)^T；
- 卡尔曼增益：K = Pf H^T (H Pf H^T + R)^−1；
- 逐成员更新：x_a = x_f + K d；
- 可选：协方差通胀（乘法/加法）与空间本地化（Gaspari–Cohn 核，按深度距离对 θ/VG 参数做局地化）。

约束与物理一致性：
- θ 约束在 [θr, θs]；
- VG 参数物理范围：θs∈(0,0.6]，θr∈[0,θs)，α>0，n>1，必要时将 m = 1−1/n；
- LAI ≥ 0，且可设上限；
- 作物参数各自边界（来自作物定义/文献）。

数值稳定性：
- 同化后对 θ 的变化幅度加“限制器”（例如逐层不超过某比例），减少跳变；
- 如果更新后的状态导致求解失败，可回退到保守缩放比率或跳过该日同化并记录。

---

## 6. 同化后的再一致化与重计算

为维持与 SWAP 内部派生量一致：
- θ 更新后：
  - 调用现有例程重算 K、C（导水率/微分含水容量）与平均导水率等（例如通过 `SoilWater(3)` 路径或专用状态刷新子程序，如有 `SoilWaterStateVar(2)` 等接口可用则优先）。
- VG 参数更新后：
  - 立即刷新与 VG 相关的函数表/缓存（位于 `WC_K_models_04_11.f90` 的函数在调用时读取参数，若存在缓存需触发刷新）。
- LAI/作物参数更新后：
  - 仅影响下一日的过程计算；无需回溯日内积分。

---

## 7. 错误处理与日志

- 统一使用 SWAP 现有 `fatalerr`/`logf` 机制：
  - 配置/观测文件缺失或不可解析 → 记录并将 `flDataAssim` 置为 false（软失败）；
  - 数值异常（协方差奇异、矩阵求逆失败）→ 使用 Tikhonov 正则或对角膨胀重试；
  - 单日同化失败 → 回退到先验，记录并继续模拟。
- DA 自有日志：`da/da.log`，包含每日日志、创新统计（O−H x）与 RMSE 轨迹。

---

## 8. 配置与文件组织

建议新增：
- `da/da_config.ini`：
  - 开关：`enable=true|false`，`frequency=daily`；
  - 集合规模：`ensemble_size=40`；
  - 误差：各变量的观测误差与初始分布；
  - 通胀与本地化参数；
  - 观测文件相对路径。
- `obs/*.csv`：观测数据。

文件/目录结构（新增部分）：
```
swap420/
  da/
    swap_assimilation_enkf.f90
    swap_assim_config.f90
    obs_reader.f90 (可选)
    da_config.ini (示例)
obs/
  soil_moisture.csv
  lai.csv
  params.csv
```

---

## 9. 具体实现步骤（里程碑）

M1 方案定稿与接口冻结（当前文档）
- 确认同化量、观测格式与白名单参数。
- 固化 3 个微创调用点与编译/运行开关。

M2 模块骨架与配置/观测读取
- 新建 `swap_assimilation_enkf.f90`，实现 `DA_Init/DA_AssimilateDaily/DA_Finalize` 空函数体与日志；
- 解析 `da_config.ini`，读取开关、集合规模、误差等；
- 观测读取与每日聚合（θ/LAI/参数）。

M3 状态向量装配/回写与观测算子
- θ/LAI/参数的装配、物理边界、白名单与映射（节点→层平均可选）；
- H 的实现与快速抽取。

M4 EnKF 核心
- 集合扰动生成、分析步（含正则化、通胀、本地化）；
- 数值稳健性处理与失败回退。

M5 微创集成与验证
- 在 `swap.f90` 三处插入调用（受 `flDataAssim` 或配置控制）；
- 小样例（无观测/假观测）跑通，验证对原结果零侵入（关闭 DA 时答案一致）。

M6 测试与基准
- 单元测试：观测读取/H/边界投影；
- 集成测试：日序列同化、RMSE 降低；
- 性能评估：集合规模对耗时影响。

---

## 10. 测试与验证策略

- 可复现实验：
  - Case A：无同化（baseline）
  - Case B：仅 LAI 同化
  - Case C：θ+LAI 同化
  - Case D：θ+LAI+VG 参数同化
- 评价指标：
  - 创新统计的零均值性、方差匹配；
  - 模拟与独立验证观测的 RMSE/BIAS；
  - 物理约束满足率与数值稳定性（失败/回退次数）。
- 自动化：
  - 编译开关或配置控制 DA；
  - 每日输出创新与分析增量。

---

## 11. 预期最小代码改动（示意）

仅在 `swap.f90` 插入 3 行（伪代码，实际按模块名与开关实现）：
- iTask == 1（初始化）附近：
  - `call DA_Init()`
- iTask == 2，`if (flDayEnd) then` 块内靠前位置（在输出与土壤营养之后、下一日控制之前）：
  - `call DA_AssimilateDaily()`
- iTask == 3（收尾）：
  - `call DA_Finalize()`

注：若需零改动主文件，可替代方案为通过 `handle_exchange`（DLL 模式）在任务 22/23/29 注入，但这要求外部驱动，不利于直接在原 SWAP 可执行内使用，故优先选择日末插桩的方案。

---

## 12. 风险与缓解

- 参数/状态维度高 → 协方差估计噪声大：使用本地化与适度通胀；
- VG 参数同化可能导致非物理曲线：严格边界与正则项；
- 观测稀疏 → 可采用层平均/垂向相关先验；
- 性能：优先 Fortran BLAS/LAPACK 求解小型线性系统（或自实现 Cholesky），避免大矩阵求逆。

---

## 13. 近期任务清单（可作为开发 Issue 列表）

1) 定稿与评审本方案（当前）
2) 创建 `swap420/da/` 目录与模块骨架文件
3) 定义 DA 配置与观测文件格式+样例
4) 实现 `DA_Init`（读配置+集合初始化）
5) 实现状态/参数装配与观测算子 H
6) 实现 EnKF 分析步、通胀与本地化（可先简化版）
7) 在 `swap.f90` 插入 3 个调用，受配置控制
8) 小样例运行与回归测试
9) 完善日志与错误处理，撰写用户文档

---

附：`swap_assimilation` API 草案（Fortran）

```fortran
module swap_assimilation
  implicit none
  private
  public :: DA_Init, DA_AssimilateDaily, DA_Finalize

contains
  subroutine DA_Init()
    ! 读取 da_config.ini，初始化集合与误差设置
  end subroutine

  subroutine DA_AssimilateDaily()
    ! 组装先验 x_f → 读入当日观测 y → 计算 K 并更新 θ/VG/LAI/参数
    ! 更新后触发必要的状态刷新（如导水率、微分含水容量）
  end subroutine

  subroutine DA_Finalize()
    ! 释放内存、输出统计
  end subroutine
end module
```

