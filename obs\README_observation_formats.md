# SWAP 数据同化观测文件格式说明

## 概述

SWAP 数据同化模块支持三类观测数据：
1. 土壤含水量 (`soil_moisture.csv`)
2. 叶面积指数 (`lai.csv`)  
3. 参数约束 (`params.csv`)

所有文件采用 CSV 格式，支持注释行（以 # 开头）。

## 1. 土壤含水量观测 (soil_moisture.csv)

### 格式
```
date,depth_cm,theta_vol,error_std
```

### 字段说明
- `date`: 观测日期，YYYY-MM-DD 格式
- `depth_cm`: 测量深度中心，单位 cm，相对地表
- `theta_vol`: 体积含水量，无量纲，范围 0.0-0.6
- `error_std`: 观测误差标准差，无量纲，典型值 0.01-0.05

### 映射到 SWAP 网格
- 观测深度将映射到最近的 SWAP 土壤节点
- 支持层平均：多个观测可映射到同一层并加权平均
- 超出模拟剖面深度的观测将被忽略

### 质量控制
- 自动剔除 theta < 0 或 theta > 0.6 的观测
- 剔除误差 <= 0 的观测
- 缺测值用 -9999 标记

## 2. 叶面积指数观测 (lai.csv)

### 格式
```
date,lai,error_std
```

### 字段说明
- `date`: 观测日期，YYYY-MM-DD 格式
- `lai`: 叶面积指数，单位 m²/m²，范围 0.0-15.0
- `error_std`: 观测误差标准差，单位 m²/m²，典型值 0.1-1.0

### 特点
- LAI 被视为空间均匀，每日最多一个有效观测
- 多个同日观测将取平均值
- 负值将被自动剔除

## 3. 参数观测/约束 (params.csv)

### 格式
```
date,param_name,layer,value,error_std
```

### 字段说明
- `date`: 约束日期，YYYY-MM-DD 格式
- `param_name`: 参数名称，必须在白名单中（见 da_config.ini）
- `layer`: 土壤层索引（1-based），0 表示剖面级参数
- `value`: 参数值，使用相应物理单位
- `error_std`: 参数误差，土壤参数用绝对值，作物参数用相对值

### 支持的参数类型

#### Van Genuchten 土壤水力参数（按层）
- `theta_r`: 残余含水量 [-]
- `theta_s`: 饱和含水量 [-] 
- `alpha`: VG 参数 [1/cm]
- `n`: VG 形状参数 [-]

#### 作物参数（剖面级，layer=0）
- `fr`, `fl`, `fs`, `fo`: 干物质分配系数 [-]
- `kdif`, `kdir`: 光消光系数 [-]
- `rgrlai`: LAI 相对增长率 [1/d]
- `amax`: 最大同化速率 [kg CO2/ha/h]
- `eff`: 光能利用效率 [kg CO2/J/ha/h/m²/s]
- `sla`: 比叶面积 [ha/kg]
- `tbase`: 发育基础温度 [°C]

### 使用建议
- 参数观测通常来自实验室分析、文献值或校准结果
- 可用于软约束（弱同化）或硬约束（强同化）
- 误差设置影响同化强度：小误差=强约束，大误差=弱约束

## 时间对齐与处理

### 日期匹配
- 所有观测按日期聚合到当日同化
- 支持不规则观测频率
- 无观测日期将跳过同化

### 坐标系统
- 深度：cm，地表为 0，向下为正
- 时间：UTC 日期，与 SWAP 模拟日期对齐
- 单位：与 SWAP 内部变量单位一致

### 数据预处理建议
- 观测前进行仪器校准和漂移校正
- 合理估计观测误差（包含仪器误差、代表性误差、尺度误差）
- 对于遥感 LAI，考虑大气校正和云遮挡影响
