# SWAP Data Assimilation Configuration
# Version: 0.1 (template)
# Date: 2025-08-31

[general]
# Enable/disable data assimilation
enable = true

# Assimilation frequency (currently only 'daily' supported)
frequency = daily

# Ensemble size for EnKF (recommended: 20-100)
ensemble_size = 40

# Debug mode (extra logging and diagnostics)
debug_mode = false

[variables]
# Which state variables and parameters to assimilate
assim_theta = true          # soil water content theta(z)
assim_vg_params = false     # Van Genuchten parameters (theta_r, theta_s, alpha, n)
assim_lai = true            # leaf area index
assim_crop_params = false   # crop model parameters (see whitelist below)

[errors]
# Observation error standard deviations
theta_obs_error = 0.02      # volumetric water content [-]
lai_obs_error = 0.5         # leaf area index [m2/m2]
vg_param_error = 0.1        # relative error for VG parameters [-]
crop_param_error = 0.05     # relative error for crop parameters [-]

# Initial ensemble spread (standard deviations)
theta_init_std = 0.01       # initial theta spread [-]
vg_init_std = 0.05          # initial VG parameter relative spread [-]
lai_init_std = 0.2          # initial LAI spread [m2/m2]
crop_init_std = 0.03        # initial crop parameter relative spread [-]

[enkf]
# EnKF algorithm parameters
inflation_factor = 1.05     # multiplicative inflation factor
localization_radius = 0.5   # localization radius [m] for soil variables
theta_change_limit = 0.1    # maximum relative theta change per day [-]

# Advanced options
use_additive_inflation = false
additive_inflation = 1.0e-6

# Innovation quality control
innovation_threshold = 3.0  # reject observations with |innovation| > k*sigma
use_robust_weights = false  # use Huber weights for outlier handling

[observations]
# Observation file paths (relative to SWAP working directory)
theta_file = obs/soil_moisture.csv
lai_file = obs/lai.csv
params_file = obs/params.csv

[crop_parameters]
# Crop parameter whitelist for assimilation
# Based on detailed analysis of SWAP cropgrowth.f90
# Format: param_name = min_value, max_value, units, description

# Allocation fractions (must sum to ~1.0, interdependent)
fr = 0.05, 0.8, "[-]", "fraction of dry matter to roots"
fl = 0.1, 0.9, "[-]", "fraction of dry matter to leaves"
fs = 0.05, 0.6, "[-]", "fraction of dry matter to stems"
fo = 0.0, 0.8, "[-]", "fraction of dry matter to storage organs"

# Light interception and photosynthesis
kdif = 0.3, 1.0, "[-]", "extinction coefficient for diffuse light"
kdir = 0.3, 1.0, "[-]", "extinction coefficient for direct light"
rgrlai = 0.001, 0.05, "[1/d]", "relative growth rate of LAI"
amax = 10.0, 80.0, "[kg CO2/ha/h]", "maximum CO2 assimilation rate"
eff = 0.2, 0.8, "[kg CO2/J/ha/h/m2/s]", "initial light use efficiency"

# Leaf and canopy properties
sla = 0.001, 0.05, "[ha/kg]", "specific leaf area"
span = 10.0, 100.0, "[d]", "leaf life span"

# Stress response and mortality
perdl = 0.0, 0.1, "[1/d]", "relative death rate due to water stress"
rsc = 10.0, 200.0, "[s/m]", "canopy resistance"

# Temperature response
tbase = -5.0, 15.0, "[C]", "base temperature for development"
q10 = 1.5, 3.0, "[-]", "Q10 factor for respiration"

# Conversion factors (dry matter to carbohydrates)
cvl = 0.6, 0.9, "[kg CH2O/kg DM]", "conversion factor leaves"
cvr = 0.6, 0.9, "[kg CH2O/kg DM]", "conversion factor roots"
cvs = 0.6, 0.9, "[kg CH2O/kg DM]", "conversion factor stems"
cvo = 0.6, 0.9, "[kg CH2O/kg DM]", "conversion factor storage organs"

# Maintenance respiration rates
rml = 0.01, 0.05, "[kg CH2O/kg DM/d]", "maintenance respiration leaves"
rmr = 0.005, 0.03, "[kg CH2O/kg DM/d]", "maintenance respiration roots"
rms = 0.005, 0.02, "[kg CH2O/kg DM/d]", "maintenance respiration stems"

[localization]
# Spatial localization settings
# Localization function: Gaspari-Cohn with distance-based correlation
theta_localization = true
vg_localization = true
lai_localization = false    # LAI is typically spatially uniform
crop_localization = false   # crop parameters are typically uniform

# Localization radii for different variable types
theta_radius = 0.5          # [m] for soil moisture
vg_radius = 1.0             # [m] for VG parameters
vertical_correlation = 0.3  # correlation between adjacent soil layers
