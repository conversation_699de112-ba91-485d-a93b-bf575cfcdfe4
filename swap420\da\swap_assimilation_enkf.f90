! File: swap_assimilation_enkf.f90
! Purpose: Data Assimilation module for SWAP using Ensemble Kalman Filter (EnKF)
! Author: Auto-generated skeleton
! Date: 2025-08-31
! Version: 0.1 (skeleton)

module swap_assimilation
  use enkf_algorithms
  use swap_assim_config
  use obs_reader
  implicit none
  private
  
  ! Public API
  public :: DA_Init, DA_AssimilateDaily, DA_Finalize
  
  ! Module parameters
  integer, parameter :: dp = kind(1.0d0)  ! double precision
  integer, parameter :: max_obs = 1000    ! maximum observations per day
  integer, parameter :: max_ensemble = 200 ! maximum ensemble size
  
  ! Module variables (private)
  logical :: da_enabled = .false.
  logical :: da_initialized = .false.
  integer :: ensemble_size = 40
  integer :: nstate = 0  ! total state vector dimension
  integer :: nobs_today = 0  ! number of observations today
  
  ! State vector layout indices
  integer :: idx_theta_start, idx_theta_end
  integer :: idx_vg_start, idx_vg_end
  integer :: idx_lai, idx_crop_start, idx_crop_end
  integer :: nvg_params_per_layer = 4  ! theta_r, theta_s, alpha, n
  integer :: ncrop_params = 0  ! will be set based on configuration

  ! VG parameter mapping (cofgen array indices)
  integer, parameter :: VG_THETAR = 1, VG_THETAS = 2, VG_ALPHA = 4, VG_N = 6

  ! Crop parameter whitelist and current values
  character(len=20), dimension(20) :: crop_param_names
  real(dp), dimension(20) :: crop_param_values, crop_param_min, crop_param_max

  ! Observation operator components
  type :: ObsOperator
    integer :: nobs_theta, nobs_lai, nobs_params
    integer, allocatable :: theta_node_map(:)     ! maps theta obs to node indices
    real(dp), allocatable :: theta_weights(:,:)   ! weights for layer averaging
    integer, allocatable :: param_indices(:)      ! maps param obs to state vector indices
    character(len=20), allocatable :: param_names(:)  ! parameter names for obs
  end type ObsOperator

  type(ObsOperator) :: H_operator
  
  ! Ensemble arrays
  real(dp), allocatable :: X_ensemble(:,:)  ! (nstate, ensemble_size)
  real(dp), allocatable :: y_obs(:)         ! observations for today
  real(dp), allocatable :: R_obs(:,:)       ! observation error covariance

  ! Additional working arrays
  real(dp), allocatable :: work_matrix(:,:)
  integer, allocatable :: work_ipiv(:)
  
  ! Configuration parameters
  real(dp) :: inflation_factor = 1.05d0
  real(dp) :: localization_radius = 0.5d0  ! meters
  real(dp) :: theta_change_limit = 0.1d0   ! maximum relative change per day
  
  ! Logging
  integer :: da_log_unit = -1
  character(len=256) :: da_log_file = 'da/da.log'

contains

  !============================================================================
  subroutine DA_Init()
    ! Initialize data assimilation module
    ! Called once during SWAP initialization (iTask=1)
    
    use variables, only: logf, numnod
    implicit none
    
    ! Local variables
    logical :: config_exists
    integer :: ierr
    
    write(logf, '(A)') 'DA_Init: Initializing data assimilation module...'
    
    ! Check if DA configuration file exists
    inquire(file='da/da_config.ini', exist=config_exists)
    if (.not. config_exists) then
      write(logf, '(A)') 'DA_Init: da_config.ini not found, DA disabled'
      da_enabled = .false.
      return
    endif
    
    ! Read configuration (placeholder)
    call DA_ReadConfig(ierr)
    if (ierr /= 0) then
      write(logf, '(A,I0)') 'DA_Init: Configuration read failed, error code: ', ierr
      da_enabled = .false.
      return
    endif
    
    if (.not. da_enabled) then
      write(logf, '(A)') 'DA_Init: DA disabled in configuration'
      return
    endif
    
    ! Open DA log file
    call DA_OpenLog()
    
    ! Determine state vector dimensions
    call DA_SetupStateVector()
    
    ! Initialize ensemble (placeholder)
    call DA_InitializeEnsemble()
    
    da_initialized = .true.
    write(logf, '(A,I0,A,I0)') 'DA_Init: Initialized with ensemble size ', &
                               ensemble_size, ', state dimension ', nstate
    
  end subroutine DA_Init

  !============================================================================
  subroutine DA_AssimilateDaily()
    ! Perform daily data assimilation
    ! Called at end of each day (flDayEnd=true)
    
    use variables, only: logf, t1900
    implicit none
    
    ! Local variables
    integer :: ierr
    character(len=20) :: date_str
    
    if (.not. da_enabled .or. .not. da_initialized) return
    
    ! Convert current time to date string for observation lookup
    call DA_TimeToDateString(t1900, date_str)
    
    write(da_log_unit, '(A,A)') 'DA_AssimilateDaily: Processing date ', trim(date_str)
    
    ! Read observations for today
    call DA_ReadObservationsDaily(date_str, ierr)
    if (ierr /= 0) then
      write(da_log_unit, '(A,I0)') 'No observations for today, error: ', ierr
      return
    endif
    
    if (nobs_today == 0) then
      write(da_log_unit, '(A)') 'No valid observations for today'
      return
    endif
    
    ! Pack current state into ensemble
    call DA_PackCurrentState()
    
    ! Perform EnKF analysis step
    call DA_EnKFAnalysis(ierr)
    if (ierr /= 0) then
      write(da_log_unit, '(A,I0)') 'EnKF analysis failed, error: ', ierr
      return
    endif
    
    ! Unpack updated state back to SWAP variables
    call DA_UnpackUpdatedState()
    
    ! Trigger SWAP state refresh (placeholder)
    call DA_RefreshSWAPStates()
    
    write(da_log_unit, '(A,I0,A)') 'Successfully assimilated ', nobs_today, ' observations'
    
  end subroutine DA_AssimilateDaily

  !============================================================================
  subroutine DA_Finalize()
    ! Finalize data assimilation module
    ! Called during SWAP closure (iTask=3)
    
    use variables, only: logf
    implicit none
    
    if (.not. da_enabled) return
    
    write(logf, '(A)') 'DA_Finalize: Closing data assimilation module...'
    
    ! Deallocate arrays
    if (allocated(X_ensemble)) deallocate(X_ensemble)
    if (allocated(y_obs)) deallocate(y_obs)
    if (allocated(R_obs)) deallocate(R_obs)
    
    ! Close log file
    if (da_log_unit > 0) then
      write(da_log_unit, '(A)') 'DA_Finalize: Data assimilation completed'
      close(da_log_unit)
    endif
    
    da_initialized = .false.
    write(logf, '(A)') 'DA_Finalize: Data assimilation module closed'
    
  end subroutine DA_Finalize

  !============================================================================
  ! Private helper subroutines (placeholders for now)
  !============================================================================
  
  subroutine DA_ReadConfig(ierr)
    integer, intent(out) :: ierr
    ! TODO: Read da_config.ini
    ierr = 0
    da_enabled = .true.  ! placeholder
  end subroutine DA_ReadConfig
  
  subroutine DA_OpenLog()
    integer :: ierr
    da_log_unit = 99  ! placeholder unit number
    open(unit=da_log_unit, file=trim(da_log_file), status='replace', iostat=ierr)
    if (ierr /= 0) da_log_unit = -1
  end subroutine DA_OpenLog
  
  subroutine DA_SetupStateVector()
    use variables, only: numnod, numlay
    use swap_assim_config, only: DA_Config
    implicit none

    ! Local variables
    type(DA_Config) :: config
    integer :: ierr

    ! Read configuration to determine which variables to assimilate
    call Config_Read('da/da_config.ini', config, ierr)
    if (ierr /= 0) then
      nstate = 0
      return
    endif

    ! Calculate state vector dimensions
    nstate = 0

    ! Theta (soil water content)
    if (config%assim_theta) then
      idx_theta_start = nstate + 1
      idx_theta_end = nstate + numnod
      nstate = nstate + numnod
    else
      idx_theta_start = 0
      idx_theta_end = 0
    endif

    ! VG parameters (per layer: theta_r, theta_s, alpha, n)
    if (config%assim_vg_params) then
      idx_vg_start = nstate + 1
      idx_vg_end = nstate + nvg_params_per_layer * numlay
      nstate = nstate + nvg_params_per_layer * numlay
    else
      idx_vg_start = 0
      idx_vg_end = 0
    endif

    ! LAI (single value)
    if (config%assim_lai) then
      idx_lai = nstate + 1
      nstate = nstate + 1
    else
      idx_lai = 0
    endif

    ! Crop parameters (based on whitelist)
    if (config%assim_crop_params) then
      call DA_SetupCropParams(config)
      idx_crop_start = nstate + 1
      idx_crop_end = nstate + ncrop_params
      nstate = nstate + ncrop_params
    else
      idx_crop_start = 0
      idx_crop_end = 0
    endif

  end subroutine DA_SetupStateVector
  
  subroutine DA_InitializeEnsemble()
    ! Initialize ensemble around current state with perturbations
    use swap_assim_config, only: DA_Config
    use variables, only: numnod, numlay, theta, cofgen, lai, layer
    implicit none

    type(DA_Config) :: config
    integer :: ierr, i, j, lay, node, idx
    real(dp) :: state_mean(nstate), perturbation
    real(dp) :: theta_std, vg_std, lai_std, crop_std

    ! Read configuration for initial spreads
    call Config_Read('da/da_config.ini', config, ierr)
    if (ierr /= 0) return

    theta_std = config%theta_init_std
    vg_std = config%vg_init_std
    lai_std = config%lai_init_std
    crop_std = config%crop_init_std

    ! Allocate ensemble array
    if (allocated(X_ensemble)) deallocate(X_ensemble)
    allocate(X_ensemble(nstate, ensemble_size))

    ! Get current state as ensemble mean
    call DA_GetCurrentStateVector(state_mean)

    ! Generate ensemble members with perturbations
    do i = 1, ensemble_size
      X_ensemble(:, i) = state_mean(:)

      ! Add perturbations to theta
      if (idx_theta_start > 0) then
        do j = 1, numnod
          call random_normal(perturbation)
          idx = idx_theta_start + j - 1
          X_ensemble(idx, i) = X_ensemble(idx, i) + perturbation * theta_std

          ! Apply constraints
          node = j
          lay = layer(node)
          X_ensemble(idx, i) = max(cofgen(VG_THETAR, node), &
                                   min(cofgen(VG_THETAS, node), X_ensemble(idx, i)))
        enddo
      endif

      ! Add perturbations to VG parameters
      if (idx_vg_start > 0) then
        call DA_PerturbVGParams(X_ensemble(:, i), vg_std)
      endif

      ! Add perturbations to LAI
      if (idx_lai > 0) then
        call random_normal(perturbation)
        X_ensemble(idx_lai, i) = X_ensemble(idx_lai, i) + perturbation * lai_std
        X_ensemble(idx_lai, i) = max(0.0d0, X_ensemble(idx_lai, i))
      endif

      ! Add perturbations to crop parameters
      if (idx_crop_start > 0) then
        call DA_PerturbCropParams(X_ensemble(idx_crop_start:idx_crop_end, i), crop_std)
      endif
    enddo

  end subroutine DA_InitializeEnsemble
  
  subroutine DA_TimeToDateString(t1900, date_str)
    real(dp), intent(in) :: t1900
    character(len=*), intent(out) :: date_str
    ! TODO: Convert SWAP time to YYYY-MM-DD format
    date_str = '2000-01-01'  ! placeholder
  end subroutine DA_TimeToDateString
  
  subroutine DA_ReadObservationsDaily(date_str, ierr)
    character(len=*), intent(in) :: date_str
    integer, intent(out) :: ierr

    ! Local variables
    type(DA_Config) :: config
    type(ObsData), dimension(max_obs) :: obs_data
    integer :: nobs_total, i, j

    ierr = 0
    nobs_today = 0

    ! Read configuration
    call Config_Read('da/da_config.ini', config, ierr)
    if (ierr /= 0) return

    ! Read observations for today
    call Obs_ReadDaily(date_str, &
                       [config%obs_theta_file, config%obs_lai_file, config%obs_params_file], &
                       obs_data, nobs_total, ierr)
    if (ierr /= 0) return

    ! Count valid observations and build observation vector
    nobs_today = 0
    do i = 1, nobs_total
      if (obs_data(i)%valid) nobs_today = nobs_today + 1
    enddo

    if (nobs_today == 0) return

    ! Allocate observation arrays
    if (allocated(y_obs)) deallocate(y_obs)
    if (allocated(R_obs)) deallocate(R_obs)
    allocate(y_obs(nobs_today))
    allocate(R_obs(nobs_today, nobs_today))

    ! Fill observation vector and error covariance
    j = 0
    do i = 1, nobs_total
      if (obs_data(i)%valid) then
        j = j + 1
        y_obs(j) = obs_data(i)%value
      endif
    enddo

    ! Build diagonal observation error covariance
    R_obs = 0.0d0
    j = 0
    do i = 1, nobs_total
      if (obs_data(i)%valid) then
        j = j + 1
        R_obs(j, j) = obs_data(i)%error**2
      endif
    enddo

    ! Build observation operator
    call DA_BuildObservationOperator(obs_data, nobs_total, ierr)

  end subroutine DA_ReadObservationsDaily
  
  subroutine DA_PackCurrentState()
    ! Pack current SWAP state into ensemble mean
    use variables, only: numnod, numlay, theta, cofgen, lai
    implicit none

    integer :: i, j, lay, node, idx
    real(dp) :: state_mean(nstate)

    if (nstate == 0) return

    ! Pack theta values
    if (idx_theta_start > 0) then
      do i = 1, numnod
        state_mean(idx_theta_start + i - 1) = theta(i)
      enddo
    endif

    ! Pack VG parameters (per layer: theta_r, theta_s, alpha, n)
    if (idx_vg_start > 0) then
      idx = idx_vg_start - 1
      do lay = 1, numlay
        node = 1  ! Use first node of each layer as representative
        do i = 1, numnod
          if (layer(i) == lay) then
            node = i
            exit
          endif
        enddo

        idx = idx + 1
        state_mean(idx) = cofgen(VG_THETAR, node)  ! theta_r
        idx = idx + 1
        state_mean(idx) = cofgen(VG_THETAS, node)  ! theta_s
        idx = idx + 1
        state_mean(idx) = cofgen(VG_ALPHA, node)   ! alpha
        idx = idx + 1
        state_mean(idx) = cofgen(VG_N, node)       ! n
      enddo
    endif

    ! Pack LAI
    if (idx_lai > 0) then
      state_mean(idx_lai) = lai
    endif

    ! Pack crop parameters
    if (idx_crop_start > 0) then
      call DA_PackCropParams(state_mean(idx_crop_start:idx_crop_end))
    endif

    ! Set ensemble mean (for now, just copy to all members)
    do i = 1, ensemble_size
      X_ensemble(:, i) = state_mean(:)
    enddo

  end subroutine DA_PackCurrentState
  
  subroutine DA_EnKFAnalysis(ierr)
    ! Implement EnKF analysis step
    use obs_reader, only: ObsData
    implicit none

    integer, intent(out) :: ierr

    ! Local variables
    integer :: i, j, info
    real(dp) :: x_mean(nstate), x_pert(nstate, ensemble_size)
    real(dp) :: y_pred(nobs_today), innovation(nobs_today)
    real(dp) :: HPH_R(nobs_today, nobs_today), K_gain(nstate, nobs_today)
    real(dp) :: obs_pert(nobs_today, ensemble_size)
    real(dp), allocatable :: work(:)
    integer, allocatable :: ipiv(:)

    ierr = 0

    if (nobs_today == 0 .or. nstate == 0) return

    write(da_log_unit, '(A,I0,A,I0)') 'EnKF Analysis: ', nobs_today, ' obs, ', nstate, ' states'

    ! Calculate ensemble mean
    x_mean = 0.0d0
    do i = 1, ensemble_size
      x_mean = x_mean + X_ensemble(:, i)
    enddo
    x_mean = x_mean / real(ensemble_size, dp)

    ! Calculate perturbations around mean
    do i = 1, ensemble_size
      x_pert(:, i) = X_ensemble(:, i) - x_mean
    enddo

    ! Apply observation operator to get predicted observations
    call DA_ApplyObservationOperator(x_mean, y_pred)

    ! Calculate innovation
    innovation = y_obs(1:nobs_today) - y_pred

    ! Generate observation perturbations (stochastic EnKF)
    call EnKF_GenerateObsPerturbations(obs_pert, R_obs, nobs_today, ensemble_size)

    ! Calculate Kalman gain using simplified approach
    call DA_CalculateSimpleKalmanGain(x_pert, Y_pert, K_gain, ierr)
    if (ierr /= 0) then
      write(da_log_unit, '(A,I0)') 'Failed to calculate Kalman gain, error: ', ierr
      return
    endif

    ! Update ensemble members
    do i = 1, ensemble_size
      real(dp) :: perturbed_innovation(nobs_today)
      perturbed_innovation = innovation + obs_pert(:, i)

      ! Apply update: x_a = x_f + K * (y + e - H*x_f)
      do j = 1, nstate
        X_ensemble(j, i) = X_ensemble(j, i) + &
                          sum(K_gain(j, :) * perturbed_innovation)
      enddo
    enddo

    ! Apply inflation
    call DA_ApplyInflation()

    ! Apply physical constraints to all ensemble members
    do i = 1, ensemble_size
      call DA_ApplyConstraints(X_ensemble(:, i))
    enddo

    write(da_log_unit, '(A)') 'EnKF Analysis completed successfully'

  end subroutine DA_EnKFAnalysis
  
  subroutine DA_UnpackUpdatedState()
    ! Unpack ensemble mean back to SWAP variables with constraints
    use variables, only: numnod, numlay, theta, cofgen, lai, layer
    implicit none

    integer :: i, j, lay, node, idx
    real(dp) :: state_mean(nstate)
    real(dp) :: theta_old(numnod)

    if (nstate == 0) return

    ! Calculate ensemble mean
    state_mean = 0.0d0
    do i = 1, ensemble_size
      state_mean = state_mean + X_ensemble(:, i)
    enddo
    state_mean = state_mean / real(ensemble_size, dp)

    ! Store old theta for change limiting
    if (idx_theta_start > 0) then
      theta_old = theta
    endif

    ! Unpack theta values with constraints and change limiting
    if (idx_theta_start > 0) then
      do i = 1, numnod
        node = i
        lay = layer(node)

        ! Get corresponding VG parameters for constraints
        real(dp) :: theta_r, theta_s, new_theta, change_ratio
        if (idx_vg_start > 0) then
          ! Use updated VG parameters
          idx = idx_vg_start + (lay - 1) * nvg_params_per_layer
          theta_r = state_mean(idx)
          theta_s = state_mean(idx + 1)
        else
          ! Use current VG parameters
          theta_r = cofgen(VG_THETAR, node)
          theta_s = cofgen(VG_THETAS, node)
        endif

        new_theta = state_mean(idx_theta_start + i - 1)

        ! Apply physical constraints
        new_theta = max(theta_r, min(theta_s, new_theta))

        ! Apply change limiting
        change_ratio = abs(new_theta - theta_old(i)) / max(theta_old(i), 1.0d-6)
        if (change_ratio > theta_change_limit) then
          ! Scale back the change
          real(dp) :: scale_factor
          scale_factor = theta_change_limit / change_ratio
          new_theta = theta_old(i) + scale_factor * (new_theta - theta_old(i))
        endif

        theta(i) = new_theta
      enddo
    endif

    ! Unpack VG parameters with constraints
    if (idx_vg_start > 0) then
      call DA_UnpackVGParams(state_mean)
    endif

    ! Unpack LAI with constraints
    if (idx_lai > 0) then
      lai = max(0.0d0, min(15.0d0, state_mean(idx_lai)))
    endif

    ! Unpack crop parameters with constraints
    if (idx_crop_start > 0) then
      call DA_UnpackCropParams(state_mean(idx_crop_start:idx_crop_end))
    endif

  end subroutine DA_UnpackUpdatedState
  
  subroutine DA_RefreshSWAPStates()
    ! Trigger SWAP to recalculate derived quantities after state update
    use variables, only: numnod
    implicit none

    integer :: i

    ! Recalculate hydraulic conductivities and capacities
    ! This mimics what happens in soilwater.f90 case(2)
    do i = 1, numnod
      ! Note: The actual recalculation will be done by SWAP's existing routines
      ! We just ensure consistency flags are set if needed
      continue
    enddo

    ! Note: In the actual integration, we may need to call specific SWAP
    ! subroutines to refresh k(i), dimoca(i), etc. This is a placeholder.

  end subroutine DA_RefreshSWAPStates

  !============================================================================
  ! State vector assembly and observation operator implementation
  !============================================================================

  subroutine DA_SetupCropParams(config)
    ! Setup crop parameter whitelist based on configuration
    use swap_assim_config, only: DA_Config
    use variables, only: logf
    implicit none

    type(DA_Config), intent(in) :: config

    ! Initialize crop parameter list (hardcoded whitelist for now)
    ncrop_params = 0

    if (config%assim_crop_params) then
      ! Allocation fractions
      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'fr'
      crop_param_min(ncrop_params) = 0.05d0
      crop_param_max(ncrop_params) = 0.8d0

      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'fl'
      crop_param_min(ncrop_params) = 0.1d0
      crop_param_max(ncrop_params) = 0.9d0

      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'fs'
      crop_param_min(ncrop_params) = 0.05d0
      crop_param_max(ncrop_params) = 0.6d0

      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'fo'
      crop_param_min(ncrop_params) = 0.0d0
      crop_param_max(ncrop_params) = 0.8d0

      ! Light parameters
      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'kdif'
      crop_param_min(ncrop_params) = 0.3d0
      crop_param_max(ncrop_params) = 1.0d0

      ncrop_params = ncrop_params + 1
      crop_param_names(ncrop_params) = 'eff'
      crop_param_min(ncrop_params) = 0.2d0
      crop_param_max(ncrop_params) = 0.8d0
    endif

    write(logf, '(A,I0,A)') 'DA_SetupCropParams: Configured ', ncrop_params, ' crop parameters'

  end subroutine DA_SetupCropParams

  subroutine DA_PackCropParams(crop_state)
    ! Pack current crop parameter values into state vector
    use variables, only: fr, fl, fs, fo, kdif, eff  ! Add more as needed
    implicit none

    real(dp), intent(out) :: crop_state(:)
    integer :: i

    do i = 1, ncrop_params
      select case (trim(crop_param_names(i)))
      case ('fr')
        crop_state(i) = fr
      case ('fl')
        crop_state(i) = fl
      case ('fs')
        crop_state(i) = fs
      case ('fo')
        crop_state(i) = fo
      case ('kdif')
        crop_state(i) = kdif
      case ('eff')
        crop_state(i) = eff
      case default
        crop_state(i) = 0.0d0  ! Default value
      end select

      ! Store current value for reference
      crop_param_values(i) = crop_state(i)
    enddo

  end subroutine DA_PackCropParams

  subroutine DA_UnpackCropParams(crop_state)
    ! Unpack crop parameters from state vector with constraints
    use variables, only: fr, fl, fs, fo, kdif, eff  ! Add more as needed
    implicit none

    real(dp), intent(in) :: crop_state(:)
    integer :: i
    real(dp) :: new_value

    do i = 1, ncrop_params
      ! Apply physical constraints
      new_value = max(crop_param_min(i), min(crop_param_max(i), crop_state(i)))

      select case (trim(crop_param_names(i)))
      case ('fr')
        fr = new_value
      case ('fl')
        fl = new_value
      case ('fs')
        fs = new_value
      case ('fo')
        fo = new_value
      case ('kdif')
        kdif = new_value
      case ('eff')
        eff = new_value
      end select
    enddo

    ! Ensure allocation fractions sum to reasonable value
    call DA_NormalizeAllocationFractions()

  end subroutine DA_UnpackCropParams

  subroutine DA_UnpackVGParams(state_vector)
    ! Unpack VG parameters with physical constraints
    use variables, only: numlay, numnod, cofgen, layer
    implicit none

    real(dp), intent(in) :: state_vector(:)
    integer :: lay, node, idx
    real(dp) :: theta_r, theta_s, alpha, n, m

    if (idx_vg_start == 0) return

    idx = idx_vg_start - 1
    do lay = 1, numlay
      ! Read parameters for this layer
      theta_r = state_vector(idx + 1)
      theta_s = state_vector(idx + 2)
      alpha = state_vector(idx + 3)
      n = state_vector(idx + 4)

      ! Apply physical constraints
      theta_s = max(0.1d0, min(0.6d0, theta_s))
      theta_r = max(0.0d0, min(theta_s - 0.01d0, theta_r))
      alpha = max(1.0d-6, min(100.0d0, alpha))
      n = max(1.001d0, min(10.0d0, n))
      m = 1.0d0 - 1.0d0/n

      ! Update all nodes in this layer
      do node = 1, numnod
        if (layer(node) == lay) then
          cofgen(VG_THETAR, node) = theta_r
          cofgen(VG_THETAS, node) = theta_s
          cofgen(VG_ALPHA, node) = alpha
          cofgen(VG_N, node) = n
          cofgen(7, node) = m  ! m parameter
        endif
      enddo

      idx = idx + nvg_params_per_layer
    enddo

  end subroutine DA_UnpackVGParams

  subroutine DA_NormalizeAllocationFractions()
    ! Ensure allocation fractions sum to 1.0
    use variables, only: fr, fl, fs, fo
    implicit none

    real(dp) :: total_frac, scale_factor

    total_frac = fr + fl + fs + fo
    if (total_frac > 0.1d0) then
      scale_factor = 1.0d0 / total_frac
      fr = fr * scale_factor
      fl = fl * scale_factor
      fs = fs * scale_factor
      fo = fo * scale_factor
    endif

  end subroutine DA_NormalizeAllocationFractions

  subroutine DA_BuildObservationOperator(obs_data, nobs, ierr)
    ! Build observation operator H for current day's observations
    use variables, only: numnod, dz, z
    use obs_reader, only: ObsData
    implicit none

    type(ObsData), intent(in) :: obs_data(nobs)
    integer, intent(in) :: nobs
    integer, intent(out) :: ierr

    integer :: i, j, node, best_node
    real(dp) :: min_dist, dist

    ierr = 0

    ! Count observation types
    H_operator%nobs_theta = 0
    H_operator%nobs_lai = 0
    H_operator%nobs_params = 0

    do i = 1, nobs
      if (.not. obs_data(i)%valid) cycle

      select case (trim(obs_data(i)%variable))
      case ('theta')
        H_operator%nobs_theta = H_operator%nobs_theta + 1
      case ('lai')
        H_operator%nobs_lai = H_operator%nobs_lai + 1
      case ('param')
        H_operator%nobs_params = H_operator%nobs_params + 1
      end select
    enddo

    ! Allocate mapping arrays
    if (H_operator%nobs_theta > 0) then
      allocate(H_operator%theta_node_map(H_operator%nobs_theta))
      allocate(H_operator%theta_weights(H_operator%nobs_theta, numnod))
      H_operator%theta_weights = 0.0d0
    endif

    ! Build theta observation mapping
    j = 0
    do i = 1, nobs
      if (.not. obs_data(i)%valid .or. trim(obs_data(i)%variable) /= 'theta') cycle

      j = j + 1

      ! Find closest node to observation depth
      min_dist = 1.0d10
      best_node = 1
      do node = 1, numnod
        dist = abs(z(node) - obs_data(i)%depth)
        if (dist < min_dist) then
          min_dist = dist
          best_node = node
        endif
      enddo

      H_operator%theta_node_map(j) = best_node
      H_operator%theta_weights(j, best_node) = 1.0d0  ! Direct mapping for now
    enddo

  end subroutine DA_BuildObservationOperator

  !============================================================================
  ! EnKF algorithm helper subroutines
  !============================================================================

  subroutine DA_GetCurrentStateVector(state_vector)
    ! Get current state vector from SWAP variables
    use variables, only: numnod, numlay, theta, cofgen, lai, layer
    implicit none

    real(dp), intent(out) :: state_vector(nstate)
    integer :: i, lay, node, idx

    ! Pack theta
    if (idx_theta_start > 0) then
      state_vector(idx_theta_start:idx_theta_end) = theta(1:numnod)
    endif

    ! Pack VG parameters
    if (idx_vg_start > 0) then
      idx = idx_vg_start - 1
      do lay = 1, numlay
        ! Find representative node for this layer
        node = 1
        do i = 1, numnod
          if (layer(i) == lay) then
            node = i
            exit
          endif
        enddo

        state_vector(idx + 1) = cofgen(VG_THETAR, node)
        state_vector(idx + 2) = cofgen(VG_THETAS, node)
        state_vector(idx + 3) = cofgen(VG_ALPHA, node)
        state_vector(idx + 4) = cofgen(VG_N, node)
        idx = idx + nvg_params_per_layer
      enddo
    endif

    ! Pack LAI
    if (idx_lai > 0) then
      state_vector(idx_lai) = lai
    endif

    ! Pack crop parameters
    if (idx_crop_start > 0) then
      call DA_PackCropParams(state_vector(idx_crop_start:idx_crop_end))
    endif

  end subroutine DA_GetCurrentStateVector

  subroutine DA_ApplyObservationOperator(state_vector, y_predicted)
    ! Apply observation operator H to state vector
    use variables, only: numnod
    implicit none

    real(dp), intent(in) :: state_vector(nstate)
    real(dp), intent(out) :: y_predicted(nobs_today)

    integer :: i, j, node

    ! Initialize
    y_predicted = 0.0d0

    ! Apply H for theta observations
    j = 0
    do i = 1, H_operator%nobs_theta
      j = j + 1
      node = H_operator%theta_node_map(i)
      if (idx_theta_start > 0 .and. node <= numnod) then
        y_predicted(j) = state_vector(idx_theta_start + node - 1)
      endif
    enddo

    ! Apply H for LAI observations
    do i = 1, H_operator%nobs_lai
      j = j + 1
      if (idx_lai > 0) then
        y_predicted(j) = state_vector(idx_lai)
      endif
    enddo

    ! Apply H for parameter observations (simplified)
    do i = 1, H_operator%nobs_params
      j = j + 1
      ! Parameter observation operator implementation
      ! This would map to specific parameter indices in state vector
      y_predicted(j) = 0.0d0  ! Placeholder
    enddo

  end subroutine DA_ApplyObservationOperator

  subroutine random_normal(x)
    ! Generate random normal variable (Box-Muller method)
    implicit none
    real(dp), intent(out) :: x

    real(dp), save :: spare
    logical, save :: has_spare = .false.
    real(dp) :: u, v, mag

    if (has_spare) then
      x = spare
      has_spare = .false.
    else
      do
        call random_number(u)
        call random_number(v)
        u = 2.0d0 * u - 1.0d0
        v = 2.0d0 * v - 1.0d0
        mag = u*u + v*v
        if (mag < 1.0d0 .and. mag > 0.0d0) exit
      enddo
      mag = sqrt(-2.0d0 * log(mag) / mag)
      x = u * mag
      spare = v * mag
      has_spare = .true.
    endif

  end subroutine random_normal

  subroutine DA_ApplyInflation()
    ! Apply inflation to ensemble
    implicit none

    real(dp) :: ensemble_mean(nstate)
    integer :: i

    ! Calculate ensemble mean
    ensemble_mean = 0.0d0
    do i = 1, ensemble_size
      ensemble_mean = ensemble_mean + X_ensemble(:, i)
    enddo
    ensemble_mean = ensemble_mean / real(ensemble_size, dp)

    ! Apply multiplicative inflation
    call EnKF_ApplyInflation(X_ensemble, ensemble_mean, inflation_factor)

  end subroutine DA_ApplyInflation

  subroutine DA_ApplyConstraints(state_vector)
    ! Apply physical constraints to a single state vector
    use variables, only: numnod, numlay, layer
    implicit none

    real(dp), intent(inout) :: state_vector(nstate)
    integer :: i, lay, node, idx
    real(dp) :: theta_r, theta_s, alpha, n

    ! Constrain theta values
    if (idx_theta_start > 0) then
      do i = 1, numnod
        node = i
        lay = layer(node)

        ! Get VG parameters for this node
        if (idx_vg_start > 0) then
          idx = idx_vg_start + (lay - 1) * nvg_params_per_layer
          theta_r = state_vector(idx)
          theta_s = state_vector(idx + 1)
        else
          theta_r = cofgen(VG_THETAR, node)
          theta_s = cofgen(VG_THETAS, node)
        endif

        ! Apply theta constraints
        state_vector(idx_theta_start + i - 1) = &
          max(theta_r, min(theta_s, state_vector(idx_theta_start + i - 1)))
      enddo
    endif

    ! Constrain VG parameters
    if (idx_vg_start > 0) then
      call DA_ConstrainVGParams(state_vector)
    endif

    ! Constrain LAI
    if (idx_lai > 0) then
      state_vector(idx_lai) = max(0.0d0, min(15.0d0, state_vector(idx_lai)))
    endif

    ! Constrain crop parameters
    if (idx_crop_start > 0) then
      call DA_ConstrainCropParams(state_vector(idx_crop_start:idx_crop_end))
    endif

  end subroutine DA_ApplyConstraints

  subroutine DA_ConstrainVGParams(state_vector)
    ! Apply VG parameter constraints
    use variables, only: numlay
    implicit none

    real(dp), intent(inout) :: state_vector(nstate)
    integer :: lay, idx
    real(dp) :: theta_r, theta_s, alpha, n

    if (idx_vg_start == 0) return

    idx = idx_vg_start - 1
    do lay = 1, numlay
      theta_r = state_vector(idx + 1)
      theta_s = state_vector(idx + 2)
      alpha = state_vector(idx + 3)
      n = state_vector(idx + 4)

      ! Apply constraints
      theta_s = max(0.1d0, min(0.6d0, theta_s))
      theta_r = max(0.0d0, min(theta_s - 0.01d0, theta_r))
      alpha = max(1.0d-6, min(100.0d0, alpha))
      n = max(1.001d0, min(10.0d0, n))

      ! Update state vector
      state_vector(idx + 1) = theta_r
      state_vector(idx + 2) = theta_s
      state_vector(idx + 3) = alpha
      state_vector(idx + 4) = n

      idx = idx + nvg_params_per_layer
    enddo

  end subroutine DA_ConstrainVGParams

  subroutine DA_ConstrainCropParams(crop_state)
    ! Apply crop parameter constraints
    implicit none

    real(dp), intent(inout) :: crop_state(:)
    integer :: i

    do i = 1, ncrop_params
      crop_state(i) = max(crop_param_min(i), min(crop_param_max(i), crop_state(i)))
    enddo

  end subroutine DA_ConstrainCropParams

  subroutine DA_PerturbVGParams(state_vector, vg_std)
    ! Add perturbations to VG parameters
    use variables, only: numlay
    implicit none

    real(dp), intent(inout) :: state_vector(nstate)
    real(dp), intent(in) :: vg_std
    integer :: lay, idx
    real(dp) :: perturbation

    if (idx_vg_start == 0) return

    idx = idx_vg_start - 1
    do lay = 1, numlay
      ! Perturb each VG parameter
      call random_normal(perturbation)
      state_vector(idx + 1) = state_vector(idx + 1) * (1.0d0 + perturbation * vg_std)  ! theta_r

      call random_normal(perturbation)
      state_vector(idx + 2) = state_vector(idx + 2) * (1.0d0 + perturbation * vg_std)  ! theta_s

      call random_normal(perturbation)
      state_vector(idx + 3) = state_vector(idx + 3) * (1.0d0 + perturbation * vg_std)  ! alpha

      call random_normal(perturbation)
      state_vector(idx + 4) = state_vector(idx + 4) * (1.0d0 + perturbation * vg_std)  ! n

      idx = idx + nvg_params_per_layer
    enddo

    ! Apply constraints after perturbation
    call DA_ConstrainVGParams(state_vector)

  end subroutine DA_PerturbVGParams

  subroutine DA_PerturbCropParams(crop_state, crop_std)
    ! Add perturbations to crop parameters
    implicit none

    real(dp), intent(inout) :: crop_state(:)
    real(dp), intent(in) :: crop_std
    integer :: i
    real(dp) :: perturbation

    do i = 1, ncrop_params
      call random_normal(perturbation)
      crop_state(i) = crop_state(i) * (1.0d0 + perturbation * crop_std)
    enddo

    ! Apply constraints after perturbation
    call DA_ConstrainCropParams(crop_state)

  end subroutine DA_PerturbCropParams

  subroutine DA_GenerateObsPerturbation(obs_pert)
    ! Generate observation perturbation for one ensemble member
    implicit none

    real(dp), intent(out) :: obs_pert(nobs_today)
    integer :: i
    real(dp) :: perturbation

    do i = 1, nobs_today
      call random_normal(perturbation)
      obs_pert(i) = perturbation * sqrt(R_obs(i, i))
    enddo

  end subroutine DA_GenerateObsPerturbation

  subroutine DA_CalculateSimpleKalmanGain(X_pert, Y_pert, K_gain, ierr)
    ! Calculate Kalman gain using simplified ensemble approach
    implicit none

    real(dp), intent(in) :: X_pert(:,:)    ! (nstate, ensemble_size)
    real(dp), intent(in) :: Y_pert(:,:)    ! (nobs, ensemble_size)
    real(dp), intent(out) :: K_gain(:,:)   ! (nstate, nobs)
    integer, intent(out) :: ierr

    ! Local variables
    integer :: nstate, nobs, ne, i, j, info
    real(dp) :: scale_factor
    real(dp), allocatable :: HPH_R(:,:), PH(:,:)
    integer, allocatable :: ipiv(:)

    nstate = size(X_pert, 1)
    nobs = size(Y_pert, 1)
    ne = size(X_pert, 2)
    ierr = 0

    scale_factor = 1.0d0 / real(ne - 1, dp)

    ! Allocate working arrays
    allocate(HPH_R(nobs, nobs))
    allocate(PH(nstate, nobs))
    allocate(ipiv(nobs))

    ! Calculate PH = (1/(Ne-1)) X_pert Y_pert^T
    call dgemm('N', 'T', nstate, nobs, ne, scale_factor, &
               X_pert, nstate, Y_pert, nobs, 0.0d0, PH, nstate)

    ! Calculate HPH = (1/(Ne-1)) Y_pert Y_pert^T + R
    call dgemm('N', 'T', nobs, nobs, ne, scale_factor, &
               Y_pert, nobs, Y_pert, nobs, 0.0d0, HPH_R, nobs)

    ! Add observation error covariance
    HPH_R = HPH_R + R_obs

    ! Add small diagonal for numerical stability
    do i = 1, nobs
      HPH_R(i, i) = HPH_R(i, i) + 1.0d-8
    enddo

    ! Solve K = PH * (HPH + R)^-1 using LU decomposition
    call dgetrf(nobs, nobs, HPH_R, nobs, ipiv, info)
    if (info /= 0) then
      ierr = 1
      deallocate(HPH_R, PH, ipiv)
      return
    endif

    ! K = PH * (HPH + R)^-1, solve (HPH + R) * K^T = PH^T
    K_gain = transpose(PH)
    call dgetrs('N', nobs, nstate, HPH_R, nobs, ipiv, K_gain, nobs, info)
    if (info /= 0) then
      ierr = 2
      deallocate(HPH_R, PH, ipiv)
      return
    endif

    K_gain = transpose(K_gain)

    deallocate(HPH_R, PH, ipiv)

  end subroutine DA_CalculateSimpleKalmanGain

end module swap_assimilation
