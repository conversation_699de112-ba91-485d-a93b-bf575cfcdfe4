# Parameter observations/constraints for SWAP data assimilation
# Format: date,param_name,layer,value,error_std
# date: YYYY-MM-DD format
# param_name: parameter name from whitelist (theta_r, theta_s, alpha, n, fr, fl, fs, fo, kdif, etc.)
# layer: soil layer index (1-based for soil params, 0 for profile-level crop params)
# value: observed/constrained parameter value in appropriate units
# error_std: parameter error standard deviation (absolute for soil, relative for crop)
#
# Notes:
# - VG parameters: theta_r, theta_s (volumetric), alpha (1/cm), n (dimensionless)
# - Crop parameters: see da_config.ini [crop_parameters] section for valid names
# - Layer 0 indicates profile-level (uniform) parameters
# - Missing values should be coded as -9999
# - Used for weak constraints or parameter calibration data

date,param_name,layer,value,error_std
# Van Genuchten soil hydraulic parameters
2023-06-01,theta_s,1,0.45,0.05
2023-06-01,theta_s,2,0.42,0.05
2023-06-01,theta_r,1,0.08,0.02
2023-06-01,theta_r,2,0.06,0.02
2023-06-01,alpha,1,0.02,0.005
2023-06-01,alpha,2,0.015,0.005
2023-06-01,n,1,1.8,0.2
2023-06-01,n,2,1.6,0.2
# Crop allocation parameters (profile-level)
2023-06-15,fr,0,0.3,0.05
2023-06-15,fl,0,0.6,0.1
2023-06-15,fs,0,0.1,0.02
2023-06-15,kdif,0,0.6,0.1
2023-06-15,rgrlai,0,0.02,0.005
# Additional VG parameters for deeper layers
2023-06-20,theta_s,3,0.40,0.06
2023-06-20,alpha,3,0.012,0.003
2023-06-20,n,3,1.5,0.15
# Photosynthesis parameters
2023-07-01,amax,0,45.0,5.0
2023-07-01,eff,0,0.5,0.1
