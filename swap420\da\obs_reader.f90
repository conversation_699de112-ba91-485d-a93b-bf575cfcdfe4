! File: obs_reader.f90
! Purpose: Observation data reader for SWAP data assimilation
! Author: Auto-generated skeleton
! Date: 2025-08-31
! Version: 0.1 (skeleton)

module obs_reader
  implicit none
  private
  
  ! Public interface
  public :: ObsData, Obs_ReadDaily, Obs_QualityControl
  
  ! Parameters
  integer, parameter :: dp = kind(1.0d0)
  integer, parameter :: max_obs_per_day = 500
  integer, parameter :: max_line_length = 1000
  
  ! Observation data type
  type :: ObsData
    character(len=10) :: date          ! YYYY-MM-DD
    character(len=20) :: variable      ! 'theta', 'lai', 'param'
    character(len=20) :: param_name    ! for parameter observations
    integer :: layer_index             ! soil layer index (0 for profile-level)
    real(dp) :: depth                  ! depth in cm (for theta observations)
    real(dp) :: value                  ! observed value
    real(dp) :: error                  ! observation error (standard deviation)
    logical :: valid                   ! passed quality control
  end type ObsData

contains

  !============================================================================
  subroutine Obs_ReadDaily(date_str, obs_files, obs_data, nobs, ierr)
    ! Read all observations for a specific date from multiple files
    
    character(len=*), intent(in) :: date_str
    character(len=256), dimension(3), intent(in) :: obs_files  ! theta, lai, params
    type(ObsData), dimension(max_obs_per_day), intent(out) :: obs_data
    integer, intent(out) :: nobs, ierr
    
    ! Local variables
    integer :: i, nobs_theta, nobs_lai, nobs_params
    type(ObsData), dimension(max_obs_per_day) :: temp_obs
    
    ierr = 0
    nobs = 0
    
    ! Read theta observations
    call read_theta_file(obs_files(1), date_str, temp_obs, nobs_theta, ierr)
    if (ierr == 0 .and. nobs_theta > 0) then
      obs_data(1:nobs_theta) = temp_obs(1:nobs_theta)
      nobs = nobs_theta
    endif
    
    ! Read LAI observations
    call read_lai_file(obs_files(2), date_str, temp_obs, nobs_lai, ierr)
    if (ierr == 0 .and. nobs_lai > 0) then
      obs_data(nobs+1:nobs+nobs_lai) = temp_obs(1:nobs_lai)
      nobs = nobs + nobs_lai
    endif
    
    ! Read parameter observations
    call read_params_file(obs_files(3), date_str, temp_obs, nobs_params, ierr)
    if (ierr == 0 .and. nobs_params > 0) then
      obs_data(nobs+1:nobs+nobs_params) = temp_obs(1:nobs_params)
      nobs = nobs + nobs_params
    endif
    
    ! Apply quality control
    call Obs_QualityControl(obs_data, nobs)
    
  end subroutine Obs_ReadDaily

  !============================================================================
  subroutine Obs_QualityControl(obs_data, nobs)
    ! Apply quality control to observations
    
    type(ObsData), dimension(*), intent(inout) :: obs_data
    integer, intent(inout) :: nobs
    
    integer :: i, valid_count
    real(dp) :: theta_min = 0.0d0, theta_max = 0.6d0
    real(dp) :: lai_min = 0.0d0, lai_max = 15.0d0
    
    valid_count = 0
    
    do i = 1, nobs
      obs_data(i)%valid = .true.
      
      ! Check for missing values
      if (obs_data(i)%value < -9990.0d0 .or. obs_data(i)%error <= 0.0d0) then
        obs_data(i)%valid = .false.
        cycle
      endif
      
      ! Variable-specific range checks
      select case (trim(obs_data(i)%variable))
      case ('theta')
        if (obs_data(i)%value < theta_min .or. obs_data(i)%value > theta_max) then
          obs_data(i)%valid = .false.
        endif
      case ('lai')
        if (obs_data(i)%value < lai_min .or. obs_data(i)%value > lai_max) then
          obs_data(i)%valid = .false.
        endif
      case ('param')
        ! Parameter-specific validation will be added later
        continue
      end select
      
      if (obs_data(i)%valid) valid_count = valid_count + 1
    enddo
    
    ! Compact valid observations
    call compact_observations(obs_data, nobs, valid_count)
    nobs = valid_count
    
  end subroutine Obs_QualityControl

  !============================================================================
  ! Private helper subroutines
  !============================================================================
  
  subroutine read_theta_file(filename, date_str, obs_data, nobs, ierr)
    character(len=*), intent(in) :: filename
    character(len=*), intent(in) :: date_str
    type(ObsData), dimension(*), intent(out) :: obs_data
    integer, intent(out) :: nobs, ierr
    
    ! Local variables
    integer :: iunit, ios, line_num
    character(len=max_line_length) :: line
    character(len=10) :: file_date
    real(dp) :: depth, theta_obs, error
    logical :: file_exists, header_skipped
    
    ierr = 0
    nobs = 0
    header_skipped = .false.
    
    inquire(file=filename, exist=file_exists)
    if (.not. file_exists) then
      ierr = 1
      return
    endif
    
    iunit = 93
    open(unit=iunit, file=filename, status='old', iostat=ios)
    if (ios /= 0) then
      ierr = 2
      return
    endif
    
    line_num = 0
    do
      read(iunit, '(A)', iostat=ios) line
      if (ios /= 0) exit
      
      line_num = line_num + 1
      line = adjustl(line)
      
      ! Skip header and comments
      if (.not. header_skipped .or. len_trim(line) == 0 .or. &
          line(1:1) == '#' .or. line(1:1) == 'D' .or. line(1:1) == 'd') then
        header_skipped = .true.
        cycle
      endif
      
      ! Parse CSV line: date,depth,theta,error
      call parse_theta_line(line, file_date, depth, theta_obs, error, ios)
      if (ios /= 0) cycle
      
      ! Check if this observation is for the requested date
      if (trim(file_date) == trim(date_str)) then
        nobs = nobs + 1
        if (nobs > max_obs_per_day) then
          ierr = 3
          exit
        endif
        
        obs_data(nobs)%date = file_date
        obs_data(nobs)%variable = 'theta'
        obs_data(nobs)%param_name = ''
        obs_data(nobs)%layer_index = 0  ! will be determined later
        obs_data(nobs)%depth = depth
        obs_data(nobs)%value = theta_obs
        obs_data(nobs)%error = error
        obs_data(nobs)%valid = .true.
      endif
      
    enddo
    
    close(iunit)
    
  end subroutine read_theta_file
  
  subroutine read_lai_file(filename, date_str, obs_data, nobs, ierr)
    character(len=*), intent(in) :: filename
    character(len=*), intent(in) :: date_str
    type(ObsData), dimension(*), intent(out) :: obs_data
    integer, intent(out) :: nobs, ierr

    ! Local variables
    integer :: iunit, ios, line_num
    character(len=max_line_length) :: line
    character(len=10) :: file_date
    real(dp) :: lai_obs, error
    logical :: file_exists, header_skipped

    ierr = 0
    nobs = 0
    header_skipped = .false.

    inquire(file=filename, exist=file_exists)
    if (.not. file_exists) then
      ierr = 1
      return
    endif

    iunit = 94
    open(unit=iunit, file=filename, status='old', iostat=ios)
    if (ios /= 0) then
      ierr = 2
      return
    endif

    line_num = 0
    do
      read(iunit, '(A)', iostat=ios) line
      if (ios /= 0) exit

      line_num = line_num + 1
      line = adjustl(line)

      ! Skip header and comments
      if (.not. header_skipped .or. len_trim(line) == 0 .or. &
          line(1:1) == '#' .or. line(1:1) == 'D' .or. line(1:1) == 'd') then
        header_skipped = .true.
        cycle
      endif

      ! Parse CSV line: date,lai,error
      call parse_lai_line(line, file_date, lai_obs, error, ios)
      if (ios /= 0) cycle

      ! Check if this observation is for the requested date
      if (trim(file_date) == trim(date_str)) then
        nobs = nobs + 1
        if (nobs > max_obs_per_day) then
          ierr = 3
          exit
        endif

        obs_data(nobs)%date = file_date
        obs_data(nobs)%variable = 'lai'
        obs_data(nobs)%param_name = ''
        obs_data(nobs)%layer_index = 0
        obs_data(nobs)%depth = 0.0d0
        obs_data(nobs)%value = lai_obs
        obs_data(nobs)%error = error
        obs_data(nobs)%valid = .true.
      endif

    enddo

    close(iunit)

  end subroutine read_lai_file

  subroutine parse_lai_line(line, date_str, lai, error, ierr)
    character(len=*), intent(in) :: line
    character(len=*), intent(out) :: date_str
    real(dp), intent(out) :: lai, error
    integer, intent(out) :: ierr

    ! Parse CSV line: date,lai,error
    character(len=256) :: temp_line
    integer :: pos1, pos2, ios

    ierr = 0
    temp_line = trim(line)

    ! Find first comma (date)
    pos1 = index(temp_line, ',')
    if (pos1 == 0) then
      ierr = 1
      return
    endif
    date_str = trim(temp_line(1:pos1-1))

    ! Find second comma (lai)
    pos2 = index(temp_line(pos1+1:), ',') + pos1
    if (pos2 == pos1) then
      ierr = 2
      return
    endif
    read(temp_line(pos1+1:pos2-1), *, iostat=ios) lai
    if (ios /= 0) then
      ierr = 3
      return
    endif

    ! Read error (rest of line)
    read(temp_line(pos2+1:), *, iostat=ios) error
    if (ios /= 0) then
      ierr = 4
      return
    endif

  end subroutine parse_lai_line
  
  subroutine read_params_file(filename, date_str, obs_data, nobs, ierr)
    character(len=*), intent(in) :: filename
    character(len=*), intent(in) :: date_str
    type(ObsData), dimension(*), intent(out) :: obs_data
    integer, intent(out) :: nobs, ierr
    
    ! TODO: Implement parameter file reading
    ! Format: date,param_name,layer,value,error
    nobs = 0
    ierr = 0
    
  end subroutine read_params_file
  
  subroutine parse_theta_line(line, date_str, depth, theta, error, ierr)
    character(len=*), intent(in) :: line
    character(len=*), intent(out) :: date_str
    real(dp), intent(out) :: depth, theta, error
    integer, intent(out) :: ierr

    ! Parse CSV line: date,depth_cm,theta_vol,error_std
    character(len=256) :: temp_line
    integer :: pos1, pos2, pos3, ios

    ierr = 0
    temp_line = trim(line)

    ! Find first comma (date)
    pos1 = index(temp_line, ',')
    if (pos1 == 0) then
      ierr = 1
      return
    endif
    date_str = trim(temp_line(1:pos1-1))

    ! Find second comma (depth)
    pos2 = index(temp_line(pos1+1:), ',') + pos1
    if (pos2 == pos1) then
      ierr = 2
      return
    endif
    read(temp_line(pos1+1:pos2-1), *, iostat=ios) depth
    if (ios /= 0) then
      ierr = 3
      return
    endif

    ! Find third comma (theta)
    pos3 = index(temp_line(pos2+1:), ',') + pos2
    if (pos3 == pos2) then
      ierr = 4
      return
    endif
    read(temp_line(pos2+1:pos3-1), *, iostat=ios) theta
    if (ios /= 0) then
      ierr = 5
      return
    endif

    ! Read error (rest of line)
    read(temp_line(pos3+1:), *, iostat=ios) error
    if (ios /= 0) then
      ierr = 6
      return
    endif

  end subroutine parse_theta_line
  
  subroutine compact_observations(obs_data, nobs_total, nobs_valid)
    type(ObsData), dimension(*), intent(inout) :: obs_data
    integer, intent(in) :: nobs_total, nobs_valid
    
    integer :: i, j
    
    j = 0
    do i = 1, nobs_total
      if (obs_data(i)%valid) then
        j = j + 1
        if (j /= i) obs_data(j) = obs_data(i)
      endif
    enddo
    
  end subroutine compact_observations

end module obs_reader
