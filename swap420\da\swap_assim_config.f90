! File: swap_assim_config.f90
! Purpose: Configuration parser for SWAP data assimilation module
! Author: Auto-generated skeleton
! Date: 2025-08-31
! Version: 0.1 (skeleton)

module swap_assim_config
  implicit none
  private
  
  ! Public interface
  public :: Config_Read, Config_Write, DA_Config
  
  ! Configuration data type
  type :: DA_Config
    ! General settings
    logical :: enable = .false.
    character(len=20) :: frequency = 'daily'
    integer :: ensemble_size = 40
    logical :: debug_mode = .false.
    
    ! State variables to assimilate
    logical :: assim_theta = .true.
    logical :: assim_vg_params = .false.
    logical :: assim_lai = .true.
    logical :: assim_crop_params = .false.
    
    ! Error parameters
    real(8) :: theta_obs_error = 0.02d0      ! volumetric water content error
    real(8) :: lai_obs_error = 0.5d0         ! LAI observation error
    real(8) :: vg_param_error = 0.1d0        ! relative error for VG parameters
    real(8) :: crop_param_error = 0.05d0     ! relative error for crop parameters
    
    ! Initial ensemble spread
    real(8) :: theta_init_std = 0.01d0       ! initial theta standard deviation
    real(8) :: vg_init_std = 0.05d0          ! initial VG parameter relative std
    real(8) :: lai_init_std = 0.2d0          ! initial LAI standard deviation
    real(8) :: crop_init_std = 0.03d0        ! initial crop parameter relative std
    
    ! EnKF parameters
    real(8) :: inflation_factor = 1.05d0     ! multiplicative inflation
    real(8) :: localization_radius = 0.5d0   ! localization radius in meters
    real(8) :: theta_change_limit = 0.1d0    ! maximum relative theta change per day
    logical :: use_additive_inflation = .false.
    real(8) :: additive_inflation = 1.0d-6   ! additive inflation variance
    
    ! Observation file paths
    character(len=256) :: obs_theta_file = 'obs/soil_moisture.csv'
    character(len=256) :: obs_lai_file = 'obs/lai.csv'
    character(len=256) :: obs_params_file = 'obs/params.csv'
    
    ! Quality control
    real(8) :: innovation_threshold = 3.0d0  ! reject obs if |innovation| > k*sigma
    logical :: use_robust_weights = .false.  ! use Huber weights for outliers
    
    ! Crop parameter whitelist (to be populated)
    integer :: n_crop_params = 0
    character(len=20), dimension(50) :: crop_param_names
    real(8), dimension(50) :: crop_param_min, crop_param_max
  end type DA_Config

contains

  !============================================================================
  subroutine Config_Read(config_file, config, ierr)
    ! Read DA configuration from INI file
    
    character(len=*), intent(in) :: config_file
    type(DA_Config), intent(out) :: config
    integer, intent(out) :: ierr
    
    ! Local variables
    integer :: iunit, ios
    character(len=256) :: line, key, value
    character(len=50) :: section
    logical :: file_exists
    
    ierr = 0
    section = ''
    
    ! Check if file exists
    inquire(file=config_file, exist=file_exists)
    if (.not. file_exists) then
      ierr = 1
      return
    endif
    
    ! Open file
    iunit = 91  ! TODO: use proper unit allocation
    open(unit=iunit, file=config_file, status='old', iostat=ios)
    if (ios /= 0) then
      ierr = 2
      return
    endif
    
    ! Initialize config with defaults
    config = DA_Config()
    
    ! Read file line by line
    do
      read(iunit, '(A)', iostat=ios) line
      if (ios /= 0) exit  ! end of file or error
      
      line = adjustl(line)
      if (len_trim(line) == 0 .or. line(1:1) == '#' .or. line(1:1) == ';') cycle
      
      ! Check for section header
      if (line(1:1) == '[' .and. index(line, ']') > 0) then
        section = trim(line(2:index(line, ']')-1))
        cycle
      endif
      
      ! Parse key=value pairs
      call parse_key_value(line, key, value, ios)
      if (ios /= 0) cycle
      
      ! Process configuration based on section and key
      call process_config_item(section, key, value, config)
      
    enddo
    
    close(iunit)
    
    ! Validate configuration
    call validate_config(config, ierr)
    
  end subroutine Config_Read

  !============================================================================
  subroutine Config_Write(config_file, config, ierr)
    ! Write DA configuration to INI file (for creating templates)
    
    character(len=*), intent(in) :: config_file
    type(DA_Config), intent(in) :: config
    integer, intent(out) :: ierr
    
    integer :: iunit, ios
    
    ierr = 0
    iunit = 92  ! TODO: use proper unit allocation
    
    open(unit=iunit, file=config_file, status='replace', iostat=ios)
    if (ios /= 0) then
      ierr = 1
      return
    endif
    
    ! Write configuration sections
    write(iunit, '(A)') '# SWAP Data Assimilation Configuration'
    write(iunit, '(A)') '# Generated automatically'
    write(iunit, '(A)') ''
    
    write(iunit, '(A)') '[general]'
    write(iunit, '(A,L1)') 'enable = ', config%enable
    write(iunit, '(A,A)') 'frequency = ', trim(config%frequency)
    write(iunit, '(A,I0)') 'ensemble_size = ', config%ensemble_size
    write(iunit, '(A,L1)') 'debug_mode = ', config%debug_mode
    write(iunit, '(A)') ''
    
    write(iunit, '(A)') '[variables]'
    write(iunit, '(A,L1)') 'assim_theta = ', config%assim_theta
    write(iunit, '(A,L1)') 'assim_vg_params = ', config%assim_vg_params
    write(iunit, '(A,L1)') 'assim_lai = ', config%assim_lai
    write(iunit, '(A,L1)') 'assim_crop_params = ', config%assim_crop_params
    write(iunit, '(A)') ''
    
    write(iunit, '(A)') '[errors]'
    write(iunit, '(A,F8.4)') 'theta_obs_error = ', config%theta_obs_error
    write(iunit, '(A,F8.4)') 'lai_obs_error = ', config%lai_obs_error
    write(iunit, '(A,F8.4)') 'vg_param_error = ', config%vg_param_error
    write(iunit, '(A,F8.4)') 'crop_param_error = ', config%crop_param_error
    write(iunit, '(A)') ''
    
    write(iunit, '(A)') '[enkf]'
    write(iunit, '(A,F8.4)') 'inflation_factor = ', config%inflation_factor
    write(iunit, '(A,F8.4)') 'localization_radius = ', config%localization_radius
    write(iunit, '(A,F8.4)') 'theta_change_limit = ', config%theta_change_limit
    write(iunit, '(A)') ''
    
    write(iunit, '(A)') '[observations]'
    write(iunit, '(A,A)') 'theta_file = ', trim(config%obs_theta_file)
    write(iunit, '(A,A)') 'lai_file = ', trim(config%obs_lai_file)
    write(iunit, '(A,A)') 'params_file = ', trim(config%obs_params_file)
    
    close(iunit)
    
  end subroutine Config_Write

  !============================================================================
  ! Private helper subroutines
  !============================================================================
  
  subroutine parse_key_value(line, key, value, ierr)
    character(len=*), intent(in) :: line
    character(len=*), intent(out) :: key, value
    integer, intent(out) :: ierr
    
    integer :: eq_pos
    
    ierr = 0
    eq_pos = index(line, '=')
    if (eq_pos == 0) then
      ierr = 1
      return
    endif
    
    key = trim(adjustl(line(1:eq_pos-1)))
    value = trim(adjustl(line(eq_pos+1:)))
    
    ! Remove comments
    eq_pos = index(value, '#')
    if (eq_pos > 0) value = trim(value(1:eq_pos-1))
    eq_pos = index(value, ';')
    if (eq_pos > 0) value = trim(value(1:eq_pos-1))
    
  end subroutine parse_key_value
  
  subroutine process_config_item(section, key, value, config)
    character(len=*), intent(in) :: section, key, value
    type(DA_Config), intent(inout) :: config

    ! Process configuration items by section
    select case (trim(section))

    case ('general')
      select case (trim(key))
      case ('enable')
        read(value, *) config%enable
      case ('frequency')
        config%frequency = trim(value)
      case ('ensemble_size')
        read(value, *) config%ensemble_size
      case ('debug_mode')
        read(value, *) config%debug_mode
      end select

    case ('variables')
      select case (trim(key))
      case ('assim_theta')
        read(value, *) config%assim_theta
      case ('assim_vg_params')
        read(value, *) config%assim_vg_params
      case ('assim_lai')
        read(value, *) config%assim_lai
      case ('assim_crop_params')
        read(value, *) config%assim_crop_params
      end select

    case ('errors')
      select case (trim(key))
      case ('theta_obs_error')
        read(value, *) config%theta_obs_error
      case ('lai_obs_error')
        read(value, *) config%lai_obs_error
      case ('vg_param_error')
        read(value, *) config%vg_param_error
      case ('crop_param_error')
        read(value, *) config%crop_param_error
      case ('theta_init_std')
        read(value, *) config%theta_init_std
      case ('vg_init_std')
        read(value, *) config%vg_init_std
      case ('lai_init_std')
        read(value, *) config%lai_init_std
      case ('crop_init_std')
        read(value, *) config%crop_init_std
      end select

    case ('enkf')
      select case (trim(key))
      case ('inflation_factor')
        read(value, *) config%inflation_factor
      case ('localization_radius')
        read(value, *) config%localization_radius
      case ('theta_change_limit')
        read(value, *) config%theta_change_limit
      case ('use_additive_inflation')
        read(value, *) config%use_additive_inflation
      case ('additive_inflation')
        read(value, *) config%additive_inflation
      case ('innovation_threshold')
        read(value, *) config%innovation_threshold
      case ('use_robust_weights')
        read(value, *) config%use_robust_weights
      end select

    case ('observations')
      select case (trim(key))
      case ('theta_file')
        config%obs_theta_file = trim(value)
      case ('lai_file')
        config%obs_lai_file = trim(value)
      case ('params_file')
        config%obs_params_file = trim(value)
      end select

    case ('crop_parameters')
      ! Crop parameter definitions will be processed separately
      ! Format: param_name = min, max, units, description
      continue

    end select

  end subroutine process_config_item
  
  subroutine validate_config(config, ierr)
    type(DA_Config), intent(inout) :: config
    integer, intent(out) :: ierr

    ierr = 0

    ! Validate ensemble size
    if (config%ensemble_size < 10 .or. config%ensemble_size > max_ensemble) then
      ierr = 1
      return
    endif

    ! Validate inflation factor
    if (config%inflation_factor < 1.0d0 .or. config%inflation_factor > 2.0d0) then
      ierr = 2
      return
    endif

    ! Validate localization radius
    if (config%localization_radius < 0.0d0 .or. config%localization_radius > 10.0d0) then
      ierr = 3
      return
    endif

    ! Validate observation errors
    if (config%theta_obs_error <= 0.0d0 .or. config%theta_obs_error > 0.2d0) then
      ierr = 4
      return
    endif

    if (config%lai_obs_error <= 0.0d0 .or. config%lai_obs_error > 5.0d0) then
      ierr = 5
      return
    endif

    ! Validate initial spreads
    if (config%theta_init_std <= 0.0d0 .or. config%theta_init_std > 0.1d0) then
      ierr = 6
      return
    endif

    ! Validate change limits
    if (config%theta_change_limit <= 0.0d0 .or. config%theta_change_limit > 0.5d0) then
      ierr = 7
      return
    endif

    ! Check that at least one variable is enabled for assimilation
    if (.not. (config%assim_theta .or. config%assim_vg_params .or. &
               config%assim_lai .or. config%assim_crop_params)) then
      ierr = 8
      return
    endif

  end subroutine validate_config

end module swap_assim_config
